<?php
namespace Database\Seeders;

use App\Models\Customer;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CustomerTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Customer::query()->truncate();

        DB::table('customers')->insert([
            'first_name' => '<PERSON>',
            'last_name' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone_number' => '52304935',
            'address' => '4568 Romano Street,Cambridge',
            'company' => '<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>',
            'customer_group' => 1,
        ]);

        DB::table('customers')->insert([
            'first_name' => '<PERSON><PERSON>',
            'last_name' => 'Hamill',
            'email' => '<EMAIL>',
            'address' => '281 Doctors Drive,Santa Monica',
            'company' => 'Windler PLC',
            'phone_number' => '31043918',
            'customer_group' => 1,

        ]);

        DB::table('customers')->insert([
            'first_name' => 'Candida',
            'last_name' => 'Arpin',
            'email' => '<EMAIL>',
            'company' => '<PERSON>-Conn',
            'phone_number' => '48665323',
            'address'=>'5 Woodside Court Sevierville',
            'customer_group' => 1,
        ]);
    }
}
