<?php

namespace App\Exports;

use App\Libraries\AllSettingFormat;
use App\Models\Order;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;

class SalesPurchasesExport implements WithHeadings, FromCollection, WithMapping, WithEvents
{
    use Exportable;

    public function collection()
    {
        $total_return_query = Order::select(DB::raw('sum(total) as total_return'))
            ->where('order_type', 'sales')
            ->where('total', '<', 0)
            ->where('status', '=', 'done');

        $salesQuery = Order::select(
            'orders.order_type',
            DB::raw('abs(sum(orders.due_amount)) as due'),
            DB::raw('abs(sum(orders.total_tax)) as tax'),
            DB::raw('sum(total) as total')
        )
            ->where('order_type', 'sales')
            ->where('status', 'done')
            ->where('total', '>', 0);

        $total_purchase_return_query = Order::select(DB::raw('sum(total) as total_return_purchase'))
            ->where('order_type', 'receiving')
            ->where('total', '<', 0)
            ->where('status', '=', 'done');

        $purchaseQuery = Order::select(
            'orders.order_type',
            DB::raw('abs(sum(orders.due_amount)) as due'),
            DB::raw('abs(sum(orders.total_tax)) as tax'),
            DB::raw('sum(total) as total')
        )
            ->where('order_type', 'receiving')
            ->where('total', '>', 0);

        $total_return = $total_return_query->first();
        $total_purchase_return = $total_purchase_return_query->first();
        $sales = $salesQuery->first();
        $purchase = $purchaseQuery->first();

        $totalReturnAmount = $total_return->total_return ? $total_return->total_return : 0;
        $totalPurchaseAmount = $total_purchase_return->total_return_purchase ? $total_purchase_return->total_return_purchase : 0;

        $data = collect([
            [
                'type' => Lang::get('lang.sales'),
                'total' => $sales->total
            ],
            [
                'type' => Lang::get('lang.sales_return'),
                'total' => $totalReturnAmount
            ],
            [
                'type' => Lang::get('lang.sales_due'),
                'total' => $sales->due
            ],
            [
                'type' => Lang::get('lang.sales_tax'),
                'total' => $sales->tax
            ],
            [
                'type' => Lang::get('lang.purchase'),
                'total' => $purchase->total
            ],
        ]);

        return $data;
    }

    public function map($reportRow): array
    {
        $allSettingFormat = new AllSettingFormat;

        return [
            $reportRow['type'],
            $allSettingFormat->getCurrencySeparator(number_format((float) $reportRow['total'], 2, '.', '')),
        ];
    }

    public function headings(): array
    {
        return [
            "Type",
            "Total",
        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:B1')->applyFromArray(
                    [
                        'font' => [
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' => [
                                'color' => ['argb' => 'FFFF0000']
                            ],
                        ]
                    ],
                );

            },
        ];
    }
}
