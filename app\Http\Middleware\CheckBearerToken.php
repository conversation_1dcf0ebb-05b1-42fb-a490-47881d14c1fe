<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CheckBearerToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // The predefined token
        $predefinedToken = 'd4e56b7c9d8c12a345b1f69b7a1e6f78c4b1a3d9c7f2e8b5a9c1d3e6f7a1b9c4';

        // Extract the Bearer token from the request
        $token = $request->bearerToken();

        // Check if the token matches the predefined token
        if ($token !== $predefinedToken) {
            return response()->json(['error' => 'Unauthorized'], Response::HTTP_UNAUTHORIZED);
        }

        // Proceed with the request
        return $next($request);
    }
}
