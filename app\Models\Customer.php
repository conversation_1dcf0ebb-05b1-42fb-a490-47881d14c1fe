<?php

namespace App\Models;
use DB;

class Customer extends BaseModel
{
    protected $fillable = ['first_name','last_name', 'email', 'company', 'tin_number', 'phone_number', 'address', 'avatar', 'customer_group', 'gstin_number','state', 'roll_no', 'aadhr_card_no','gender_id', 'course_id', 'semester_year'];

    public static function getCustomers($searchValue, $typeFilter, $columnName, $columnSortedBy, $limit, $offset, $requestType)
    {

        $query = Customer::query()->leftJoin('customer_groups', 'customers.customer_group', '=', 'customer_groups.id')
                                ->leftJoin('customer_payments', 'customer_payments.customer_id', '=', 'customers.id')
								->leftJoin('product_courses', 'product_courses.id', '=', 'customers.course_id');
        if ($searchValue) {
            $query->where(function ($query) use ($searchValue) {
                $query->where('first_name', 'LIKE', '%' . $searchValue . '%');
                $query->orWhere('last_name', 'LIKE', '%' . $searchValue . '%');
                $query->orWhere('email', 'LIKE', '%' . $searchValue . '%');
                $query->orWhere('customers.roll_no', 'LIKE', '%' . $searchValue . '%');
                $query->orWhere('aadhr_card_no', 'LIKE', '%' . $searchValue . '%');
                $query->orWhere('phone_number', 'LIKE', '%' . $searchValue . '%');
            });
        }

        if (!empty($typeFilter)) {
            $query->where('customer_group', $typeFilter);
        }

        $query->select('customers.*','product_courses.course_name','customer_groups.title as customer_group_title', 'customer_payments.fees', 'customer_payments.paid', 'customer_payments.balance')->distinct();
        $count = $query->count();

        if (empty($requestType)) {
            $data = $query->orderBy($columnName, $columnSortedBy)->take($limit)->skip($offset)->get();

        } else {
            $data = $query->orderBy($columnName, $columnSortedBy)->get();

        }

        return ['data' => $data, 'count' => $count];
    }

    public static function getCustomerDetails(){
         return Customer::query()->leftJoin('customer_groups', 'customers.customer_group', '=', 'customer_groups.id')
                        ->select(
                            'customers.*',
                            'customer_groups.title as customer_group_title',
                            'customer_groups.discount as customer_group_discount'
                        )
                        ->get();
    }
    
    public static function getCustomerDetailsByFilter($searchValue){
        $query = Customer::query()
                ->leftJoin('customer_groups', 'customers.customer_group', '=', 'customer_groups.id')
                ->select(
                    'customers.id',
                    'customers.first_name',
                    'customers.last_name',
                    'customers.email',
                    'customers.roll_no',
                    'customers.aadhr_card_no',
                    'customer_groups.title as customer_group_title',
                    'customer_groups.discount as customer_group_discount'
                )
                ->when($searchValue, function ($query, $searchValue) {
                    $query->where(function ($query) use ($searchValue) {
                        $query->where('customers.first_name', 'LIKE', '%' . $searchValue . '%')
                            ->orWhere('customers.last_name', 'LIKE', '%' . $searchValue . '%')
                            ->orWhere('customers.email', 'LIKE', '%' . $searchValue . '%')
                            ->orWhere('customers.roll_no', 'LIKE', '%' . $searchValue . '%')
                            ->orWhere('customers.aadhr_card_no', 'LIKE', '%' . $searchValue . '%');
                    });
                });

        return $query->take(50)->get();
   }

    public static function getCustomerDetailsDuplicate(){
        return Customer::query()->leftJoin('customer_groups', 'customers.customer_group', '=', 'customer_groups.id')
                       ->select(
                           'customers.*',
                           'customer_groups.title as customer_group_title',
                           'customer_groups.discount as customer_group_discount'
                       )
                       ->where('customers.id',0)// remove
                       ->get();
   }

    public static function customerDetails($id)
    {
        $customerDetails = Customer::where('customers.id', $id)->leftJoin('customer_groups', 'customers.customer_group', '=', 'customer_groups.id')
            ->select('customers.*', 'customer_groups.title as customer_group_title')->first();
        $customerDetails->fullName = $customerDetails->first_name . " " . $customerDetails->last_name;


        return $customerDetails;
    }

    public static function customerData($searchValue)
    {
        $query = Customer::query()->join('customer_groups', 'customers.customer_group', '=', 'customer_groups.id')
            ->select('customers.*','customer_groups.discount as customer_group_discount');

        if ($searchValue) {
            $query->where(function ($query) use ($searchValue) {
                $query->where('first_name', 'LIKE', '%' . $searchValue . '%')
                    ->orWhere('last_name', 'LIKE', '%' . $searchValue . '%')
                    ->orWhere('email', 'LIKE', '%' . $searchValue . '%');

            });
        }

        return $query->orderBy('id', 'DESC')->get();
    }

    public static function getCustomerGroup($customerGroup)
    {
        $query = CustomerGroup::query()->select('*');

        if ($customerGroup != null) {
            return $query->where('title',$customerGroup)->select('id')->first();
        }
    }

    // public static function getCustomerCombo($filtersData, $searchValue, $columnSortedBy, $limit, $offset, $columnName, $requestType)
    // {
    //     $query = Customer::query()
    //         ->leftJoin('customer_groups', 'customers.customer_group', '=', 'customer_groups.id')
    //         ->leftJoin('customer_payments', 'customer_payments.customer_id', '=', 'customers.id')
    //         ->leftJoin('product_courses', 'product_courses.id', '=', 'customers.course_id')
    //         ->leftJoin('product_packages', function($join) {
    //             $join->on('product_packages.course_id', '=', 'product_courses.id')
    //                 ->on('product_packages.package_for', '=', 'customers.gender_id'); // Added gender_id condition
    //         })
    //         ->leftJoin('product_package_products', 'product_package_products.package_id', '=', 'product_packages.id')
    //         ->leftJoin('products', 'products.id', '=', 'product_package_products.product_id')
    //         ->select(
    //             'customers.id',
    //             'customers.first_name',
    //             'customers.last_name',
    //             DB::raw("CONCAT(customers.first_name, ' ', customers.last_name) AS full_name"),
    //             'customers.email',
    //             'customers.roll_no',
    //             'customers.aadhr_card_no',
    //             'customers.company',
    //             'customers.gender_id',
    //             'customers.phone_number',
    //             'product_courses.course_name',
    //             'product_packages.package_name',
    //             'products.title',
    //             'product_package_products.quantity',
    //             'product_package_products.product_id',
    //         );

    //     if ($searchValue) {
    //         $query->where(function ($query) use ($searchValue) {
    //             $query->where('customers.first_name', 'LIKE', '%' . $searchValue . '%')
    //                 ->orWhere('customers.last_name', 'LIKE', '%' . $searchValue . '%')
    //                 ->orWhere('customers.email', 'LIKE', '%' . $searchValue . '%')
    //                 ->orWhere('customers.roll_no', 'LIKE', '%' . $searchValue . '%')
    //                 ->orWhere('customers.aadhr_card_no', 'LIKE', '%' . $searchValue . '%');
    //         });
    //     }

    //     if (!empty($filtersData)) {
    //         foreach ($filtersData as $singleFilter) {
    //             if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "gender") {
    //                 $query->where('customers.gender_id', $singleFilter['value']);
    //             }
    //             if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "course") {
    //                 $query->where('customers.course_id', $singleFilter['value']);
    //             }
    //             if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "product_name") {
    //                 $query->where('products.id', $singleFilter['value']);
    //             }
    //         }
    //     }

    //     $count = $query->count();

    //     $data = $query->orderBy($columnName, $columnSortedBy)
    //         ->take($limit)
    //         ->skip($offset)
    //         ->get();

    //     return ['data' => $data, 'count' => $count];
    // }

    public static function getCustomerCombo($filtersData, $searchValue, $columnSortedBy, $limit, $offset, $columnName)
    {
        $query = Customer::query()
            ->leftJoin('customer_groups', 'customers.customer_group', '=', 'customer_groups.id')
            ->leftJoin('customer_payments', 'customer_payments.customer_id', '=', 'customers.id')
            ->leftJoin('product_courses', 'product_courses.id', '=', 'customers.course_id')
            ->leftJoin('product_packages', function ($join) {
                $join->on('product_packages.course_id', '=', 'product_courses.id')
                    ->on('product_packages.package_for', '=', 'customers.gender_id');
            })
            ->leftJoin('product_package_products', 'product_package_products.package_id', '=', 'product_packages.id')
            ->leftJoin('products', 'products.id', '=', 'product_package_products.product_id')
            ->leftJoin(DB::raw("(SELECT orders.customer_id, order_items.product_id, ABS(SUM(order_items.quantity)) AS issued_quantity 
                                FROM order_items 
                                LEFT JOIN orders ON orders.id = order_items.order_id 
                                WHERE orders.status = 'done' AND orders.order_type = 'sales' AND orders.customer_id IS NOT NULL 
                                GROUP BY order_items.product_id, orders.customer_id) AS rec"), function ($join) {
                $join->on('rec.product_id', '=', 'product_package_products.product_id')
                    ->on('rec.customer_id', '=', 'customers.id');
            })

            ->leftJoin(DB::raw("(SELECT customers.id as customer_id , order_items.product_id, '1' as is_jeans from  orders left join order_items on order_items.order_id = orders.id 
            left join customers on customers.id = orders.customer_id and customers.gender_id = 'F'
            WHERE order_items.product_id IN (62,64) and orders.customer_id IS NOT NULL AND orders.status = 'done'
            GROUP BY orders.customer_id) AS cust62t_64j"), function ($join) {
                $join->on('cust62t_64j.customer_id', '=', 'customers.id');
            })

            ->leftJoin(DB::raw("(SELECT customers.id as customer_id , order_items.product_id, '1' as is_kurti from  orders left join order_items on order_items.order_id = orders.id 
            left join customers on customers.id = orders.customer_id and customers.gender_id = 'F'
            WHERE order_items.product_id IN (76,82) and orders.customer_id IS NOT NULL AND orders.status = 'done'
            GROUP BY orders.customer_id) AS cust76k_82l"), function ($join) {
                $join->on('cust76k_82l.customer_id', '=', 'customers.id');
            })
 

            ->select(
                'customers.id',
                'customers.first_name',
                'customers.last_name',
                DB::raw("CONCAT(customers.first_name, ' ', customers.last_name) AS full_name"),
                'customers.email',
                'customers.roll_no',
                'customers.aadhr_card_no',
                'customers.company',
                'customers.gender_id',
                'customers.phone_number',
                'product_courses.course_name',
                'product_packages.package_name',
                'products.title',
                'product_package_products.product_id',
                'product_package_products.quantity',
                DB::raw("IFNULL(rec.issued_quantity, 0) AS issued_quantity"),
                DB::raw("(product_package_products.quantity - IFNULL(rec.issued_quantity, 0)) AS pending_quantity"),
                'cust62t_64j.is_jeans','cust76k_82l.is_kurti'
            )
            ->whereRaw("(product_package_products.quantity - IFNULL(rec.issued_quantity, 0)) > 0 AND customers.customer_group = 1 ")
            ->whereNotNull('customers.course_id');

        if ($searchValue) {
            $query->where(function ($query) use ($searchValue) {
                $query->where('customers.first_name', 'LIKE', '%' . $searchValue . '%')
                    ->orWhere('customers.last_name', 'LIKE', '%' . $searchValue . '%')
                    ->orWhere('customers.email', 'LIKE', '%' . $searchValue . '%')
                    ->orWhere('customers.roll_no', 'LIKE', '%' . $searchValue . '%')
                    ->orWhere('customers.aadhr_card_no', 'LIKE', '%' . $searchValue . '%');
            });
        }

        if (!empty($filtersData)) {
            foreach ($filtersData as $singleFilter) {
                if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "gender") {
                    $query->where('customers.gender_id', $singleFilter['value']);
                }
                if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "course") {
                    $query->where('customers.course_id', $singleFilter['value']);
                }
                if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "product_name") {
                    $query->where('products.id', $singleFilter['value']);
                }
            }
        }

        $count = $query->count();

        $data = $query->orderBy($columnName, $columnSortedBy)
            ->take($limit)
            ->skip($offset)
            ->get();

        return ['data' => $data, 'count' => $count];
    }

}
