<?php
// app/Models/PurchaseQuotation.php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PurchaseQuotation extends BaseModel
{
    use HasFactory;

    protected $fillable = ['supplier_id', 'file_upload', 'total', 'order_id', 'created_by', 'status'];

    protected $table = 'purchase_quotations'; 

    // Define the relationship with Supplier
    public function supplier()
    {
        return $this->belongsTo(Supplier::class);
    }

    // Define the relationship with Order
    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}
