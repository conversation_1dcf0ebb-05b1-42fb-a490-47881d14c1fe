<?php

namespace App\Http\Controllers\Cron;

use App\Libraries\AllSettingFormat;
use App\Models\OrderItems;
use App\Models\Setting;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use PDF;
use File;
use App\Libraries\Email;
use Illuminate\Support\Facades\Storage;
 

class TestController extends Controller
{
    public function TestCode()
    {
        $filePath = public_path('uploads/updated1.csv');
        // Check if the file exists
        if (!file_exists($filePath) || !is_readable($filePath)) {
            return response()->json(['error' => 'File not found or not readable'], 404);
        }

        $data = [];
        
        // Open the file for reading
        if (($handle = fopen($filePath, 'r')) !== false) {
            // Loop through each row of the file
            while (($row = fgetcsv($handle, 1000, ",")) !== false) {
                $data[] = $row;  // Add each row to the data array
            }
            fclose($handle); // Close the file after reading
        }

        // Process $data or return it as needed

        if(!empty($data)){
            $init = 0;
            foreach($data as $key => $rec){
                
                $message = '';
                $roll_no = $phone = $email = '';
                $roll_no = $rec[0];
                $phone = $rec[1];
                // echo "<pre>"; print_r($rec); echo "</pre>";
                // \DB::enableQueryLog();
                $roll_no_old_obj = \DB::table('customers')->where("roll_no", $roll_no)->get();
                // dd(DB::getQueryLog());
                // dd($roll_no_old_obj);

                if(!empty($roll_no_old_obj)){
                    foreach($roll_no_old_obj as $key1 => $rec1){
                        $id = $rec1->id;
                        $roll_no_old    = isset($rec1->roll_no) ? $rec1->roll_no : ''; 
                        $aadhar_old     = isset($rec1->aadhr_card_no) ? $rec1->aadhr_card_no : ''; 
                        $roll_no_old_len = strlen($roll_no_old);

                        if($aadhar_old == "" && $roll_no_old_len == 12){
                            $init++;
                           \DB::table('customers')->where('id', $id)->update([ 'aadhr_card_no' => $roll_no_old ]);                                 
                        }

                       \DB::table('customers')->where('id', $id)->update([ 'roll_no' => $phone ]);

                        $message .= "<pre>   roll no : $roll_no_old ---- to ---- $phone updated </pre>\n ";
                        print $message;

                    }
                } 
            }
        }
        // return response()->json($data);

        
    }

    public function UpdateCode()
    {
        $filePath = public_path('uploads/phone-up.csv');
        // Check if the file exists
        if (!file_exists($filePath) || !is_readable($filePath)) {
            return response()->json(['error' => 'File not found or not readable'], 404);
        }

        $data = [];
        
        // Open the file for reading
        if (($handle = fopen($filePath, 'r')) !== false) {
            // Loop through each row of the file
            while (($row = fgetcsv($handle, 1000, ",")) !== false) {
                $data[] = $row;  // Add each row to the data array
            }
            fclose($handle); // Close the file after reading
        }

        if(!empty($data)){
            $init =0;
            unset($data[0]);
            foreach($data as $key => $rec){
 
                
                $message        = '';
                $roll_no        = isset($rec[0]) ? $rec[0] : '';
                $old_ph         = isset($rec[1]) ? trim($rec[1]) : ''; 
                $new_ph         = isset($rec[2]) ? trim($rec[2]) : ''; 


                $init++;
                if($roll_no != ""){

                    $roll_no_old_obj = \DB::table('customers')->where("roll_no", $roll_no)->where("phone_number", $old_ph)->first(); 
                    // $roll_no_old_obj = \DB::table('customers')->where("roll_no", $roll_no)->first(); 
                    if(!empty($roll_no_old_obj)){
                        \DB::table('customers')->where('id', $roll_no_old_obj->id)->update([ 'phone_number' => $new_ph ]);

                    }else{
                        $roll__obj = \DB::table('customers')->where("phone_number", $old_ph)->first(); 
                        if(!empty($roll__obj))
                            \DB::table('customers')->where('id', $roll__obj->id)->update([ 'phone_number' => $new_ph ]);
                        else
                        echo "<prE>   ---'.'roll '.$roll_no. 'old from ph ".$old_ph." new ph ".$new_ph;
                    }
                    
                }  
            }
            dd($init);
        }
    }


    public function RemoveStudents()
    {
        $filePath = public_path('uploads/student.csv');
        // Check if the file exists
        if (!file_exists($filePath) || !is_readable($filePath)) {
            return response()->json(['error' => 'File not found or not readable'], 404);
        }

        $data = [];
        
        // Open the file for reading
        if (($handle = fopen($filePath, 'r')) !== false) {
            // Loop through each row of the file
            while (($row = fgetcsv($handle, 1000, ",")) !== false) {
                $data[] = $row;  // Add each row to the data array
            }
            fclose($handle); // Close the file after reading
        }

        if(!empty($data)){
            $init = [];
            unset($data[0]);
            $init = 0;
            foreach($data as $key => $rec){
                // echo "<pre>"; echo $key; echo "</pre>";
                $roll_no    = isset($rec[0]) ? $rec[0] : '';
                $phone      = isset($rec[1]) ? $rec[1] : '';
                $email      = isset($rec[2]) ? $rec[2] : '';

                $cust_obj = \DB::table('customers')->whereRaw('LOWER(roll_no) = ?', [strtolower($roll_no)])->first();
                if(empty($cust_obj)){
                    // echo "<pre>"; echo $roll_no; echo "</pre>";
                }else{
                    $cust_id = null;
                    $cust_id = $cust_obj->id;

                    $order_obj = \DB::table('orders')->where("customer_id", $cust_id)->first();
                    if(!empty($order_obj)){
                        $init++;
                        echo "<pre> order exists"; echo $cust_id; echo "---------------- $init </pre>";
                    }else{
                        // \DB::table('customers')->where("id", $cust_id)->delete();
                    }
                    
                }
            }
           
        }

        echo "done";
    }
    public function RemoveDuplicateStudents(){
        

        // Run the query
        $duplicateEmails = DB::table('customers')
            ->select(
                DB::raw('COUNT(*) as cnt'),
                DB::raw('GROUP_CONCAT(id) as ids'),
                DB::raw('GROUP_CONCAT(roll_no) as roll_nos'),
                DB::raw('GROUP_CONCAT(phone_number) as phone_numbers'),
                DB::raw('GROUP_CONCAT(first_name) as first_names'),
                DB::raw('GROUP_CONCAT(email) as emails')
            )
            ->where('email', '!=', '')
            ->whereNotNull('roll_no')
            ->whereNotNull('email')
            ->groupBy('email')
            ->having('cnt', '>', 1)
            ->get();

        // Loop through the results
        $init = 0;
        foreach ($duplicateEmails as $duplicate) {

            $roll_nos = explode(',',$duplicate->roll_nos);
            $ids = explode(',',$duplicate->ids);
            

            $roll1 = $roll_nos[0];
            $roll2 = $roll_nos[1];

            $count = DB::table('orders')
                ->whereIn('customer_id', $ids)
                ->select('customer_id')
                ->distinct()
                ->count('customer_id');
            
            // Return true if both IDs have data, else return false
            if ($count == count($ids)) {
                // echo "<pre> yes orders </prE>" ;
            } else {
                $roll_no_old_obj1_id = \DB::table('customers')->where("roll_no", $roll1)->first()->id; 
                $roll_no_new_obj1_id = \DB::table('customers')->where("roll_no", $roll2)->first()->id; 

                if (!$this->isValidRollNumber($roll1)) {
                    echo "<br>Old Roll ($roll1) is invalid. New Roll ($roll2) is valid.<br>";
                    $order_obj = \DB::table('orders')->where("customer_id", $roll_no_old_obj1_id)->count();
                    if($order_obj){
                        $init++;
                        //echo "need to update $init <br>";
                        \DB::table('customers')->where('id', $roll_no_old_obj1_id)->update([ 'roll_no' => $roll2 ]);
                         \DB::table('customers')->where("id", $roll_no_new_obj1_id)->delete();
                    }

                } 
            }              
        }


        foreach ($duplicateEmails as $duplicate) {

            $roll_nos = explode(',',$duplicate->roll_nos);
            $ids = explode(',',$duplicate->ids);
            

            $roll1 = $roll_nos[0];
            $roll2 = $roll_nos[1];

            $count = DB::table('orders')
                ->whereIn('customer_id', $ids)
                ->select('customer_id')
                ->distinct()
                ->count('customer_id');
            
            // Return true if both IDs have data, else return false
            if ($count == count($ids)) {
                // echo "<pre> yes orders </prE>" ;
            } else {
                $roll_no_old_obj1_id = \DB::table('customers')->where("roll_no", $roll1)->first()->id; 
                $roll_no_new_obj1_id = \DB::table('customers')->where("roll_no", $roll2)->first()->id; 

                

                if ($this->isValidRollNumber($roll2)) {
                    // echo "<br>New Roll ($roll2) is valid.<br>";
                    // $order_objx = \DB::table('orders')->where("customer_id", $roll_no_new_obj1_id)->count();                 
                    // if(!$order_objx){
                    //     echo "$roll_no_new_obj1_id need to update<br> ";
                    // }
                }else{
                    // echo "<br>New Roll ($roll2) is invalid.<br>";
                    $order_objx = \DB::table('orders')->where("customer_id", $roll_no_old_obj1_id)->count();  
                    if(!$order_objx){
                        echo "$roll_no_old_obj1_id need to update $roll1 => $roll1 ||| count $order_objx <br>";
                        \DB::table('customers')->where("id", $roll_no_old_obj1_id)->delete();
                    }  
                }
            }

            
            

             
        }


    }
    function isValidRollNumber($roll) {        
        return preg_match('/[A-Za-z]/', $roll) && preg_match('/[0-9]/', $roll);
    }
}
