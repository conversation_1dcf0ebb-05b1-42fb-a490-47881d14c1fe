<?php

namespace App\Http\Controllers\Cron;

use App\Libraries\AllSettingFormat;
use App\Models\OrderItems;
use App\Models\Payments;
use App\Models\PaymentType;
use App\Models\Setting;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use PDF;
use File;
use App\Libraries\Email;
use Illuminate\Support\Facades\Storage;
 

class CronController extends Controller
{
    public function salesReportCron()
    {
        $payment_summary    = []; 
        $sales              = [];

        $salesReportEmail   = Setting::getSettingValue('sales_report_email')->setting_value;
        $salesReportEmails  = Setting::getSettingValue('sales_report_emails')->setting_value;

        if($salesReportEmail == '1'){
            $salesReportEmails = ($salesReportEmails != '') ? $salesReportEmails : '';
            $salesReportEmailsAry = explode(',', $salesReportEmails);

            $sales              = OrderItems::salesItemsCron();
            $payment_types      = PaymentType::getPaymentTypes(); 
            $payment_summary    = Payments::paymentReportListCron(); 

            // return view('reports.reportPdfView', [ 'payments' => $sales , 'payment_types' => $payment_types, 'payment_summary' => $payment_summary ]);
            
            $subject    = 'Sales Report of '.date('d M Y');
            $email      = '<EMAIL>';
            $mailText   = "<html> 
                            <body> 
                            <p>Hi, </p>
                            
                            <p>Hope you're having a great day. <br>
                            Sales report of ".date('d M Y')." is now available. <br>
                            Please find the report in the below attachment.</p>
                           
                            <p>Thanks, <br> <br>
                            Stellifyflows.</p>

                            </body>
                            </html>";

        
            $emailSend = new Email;     
            $fileNameToStore = "Sales Report ".date('dMY').".pdf";
            $pdf = PDF::loadView('reports.reportPdfView', [ 'payments' => $sales , 'payment_types' => $payment_types, 'payment_summary' => $payment_summary ] )
            ->setPaper('a4', 'landscape')
            ->setWarnings(false);

            $content    = $pdf->download()->getOriginalContent();

            Storage::put('public/pdf/'.$fileNameToStore,$content);
            $url = env('APP_URL');
            $_pdf =     $url.'public/pdf/'.$fileNameToStore;
            $_pdf =     storage_path('app/public/pdf/'.$fileNameToStore);   

            if(!empty($salesReportEmailsAry)){
                foreach ($salesReportEmailsAry as $key => $email) {
                    $emailSend->sendEmail($mailText, $email, $subject, $fileNameToStore);
                }
                
            }
            
            unlink($_pdf); 
        }
    }
}
