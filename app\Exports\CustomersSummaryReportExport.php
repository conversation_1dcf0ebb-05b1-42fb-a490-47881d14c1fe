<?php

namespace App\Exports;

use App\Libraries\AllSettingFormat;
use App\Models\Order;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;

class CustomersSummaryReportExport implements WithHeadings, FromCollection, WithMapping, WithEvents
{
    use Exportable;

    protected $data;

    public function __construct()
    {
        $this->data = $this->prepareData();
    }

    public function prepareData()
    {
        $data = Order::where('orders.order_type', 'sales')
            ->leftJoin('customers', 'customers.id', 'orders.customer_id')
            ->where('orders.type', 'customer')
            ->where('orders.status', '=', 'done')
            ->groupBy('orders.customer_id')
            ->select(
                'orders.customer_id',
                DB::raw("CONCAT(customers.first_name,' ',customers.last_name) AS name"),
                DB::raw('abs(sum(orders.due_amount)) as due')
            )
            ->orderBy('name', 'ASC')
            ->get();

        foreach ($data as $item) {
            $total_sales = Order::select(DB::raw('abs(sum(total)) as total_sales'))
                ->where('status', 'done')
                ->where('orders.type', 'customer')
                ->where('customer_id', $item->customer_id)
                ->where('total', '>', 0)
                ->first();
            $item->total_sales = $total_sales->total_sales ?? 0;

            $total_return = Order::select(DB::raw('abs(sum(total)) as total_return'))
                ->where('status', 'done')
                ->where('orders.type', 'customer')
                ->where('customer_id', $item->customer_id)
                ->where('total', '<', 0)
                ->first();
            $item->total_return = $total_return->total_return ?? 0;

            $total_payment = Order::select(DB::raw('abs(sum(total - due_amount)) as total_payment'))
                ->where('status', 'done')
                ->where('orders.type', 'customer')
                ->where('customer_id', $item->customer_id)
                ->where('total', '>', 0)
                ->first();
            $item->total_payment = $total_payment->total_payment ?? 0;
        }

        return $data;
    }

    public function collection()
    {
        return $this->data;
    }

    public function map($reportRow): array
    {
        $allSettingFormat = new AllSettingFormat;
        return [
            $reportRow->name == null ? $reportRow->name = Lang::get('lang.walk_in_customer') : $reportRow->name,
            $allSettingFormat->getCurrencySeparator(number_format((float) $reportRow->total_sales, 2, '.', '')),
            $allSettingFormat->getCurrencySeparator(number_format((float) $reportRow->total_return, 2, '.', '')),
            $allSettingFormat->getCurrencySeparator(number_format((float) $reportRow->total_payment, 2, '.', '')),
            $allSettingFormat->getCurrencySeparator(number_format((float) $reportRow->due, 2, '.', '')),
        ];
    }

    public function headings(): array
    {
        return [
            "Customer",
            "Total Sales",
            "Total Return",
            "Total Payment",
            "Due"
        ];
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:E1')->applyFromArray(
                    [
                        'font' => [
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' => [
                                'color' => ['argb' => 'FFFF0000']
                            ],
                        ]
                    ]
                );

                $query_result = $this->data;
                $rows_count = count($query_result);

                $total_sales = $query_result->sum('total_sales');
                $total_return = $query_result->sum('total_return');
                $total_payment = $query_result->sum('total_payment');
                $due = $query_result->sum('due');

                $rows_count += 3;

                $allSettingFormat = new AllSettingFormat;
                $event->sheet->setCellValue('A' . $rows_count, 'Grand Total');
                $event->sheet->setCellValue('B' . $rows_count, $allSettingFormat->getCurrencySeparator($total_sales));
                $event->sheet->setCellValue('C' . $rows_count, $allSettingFormat->getCurrencySeparator($total_return));
                $event->sheet->setCellValue('D' . $rows_count, $allSettingFormat->getCurrencySeparator($total_payment));
                $event->sheet->setCellValue('E' . $rows_count, $allSettingFormat->getCurrencySeparator($due));
            },
        ];
    }
}
