<?php
namespace Database\Seeders;

use App\Models\Supplier;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SupplierTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Supplier::query()->truncate();
        DB::table('suppliers')->insert([
            'first_name' => 'Adam',
            'last_name' => '<PERSON>',
            'email' => '<EMAIL>',
            'company' => 'Buckridge, Rosenbaum and Senger',
            'phone_number' => '77255956',
            'address'=>'4857 Rinehart Road , Tamarac',

        ]);
        DB::table('suppliers')->insert([
            'first_name' => '<PERSON>',
            'last_name' => '<PERSON>',
            'email' => '<EMAIL>',
            'company' => 'Welch Inc',
            'phone_number' => '88136835',
            'address'=>'3582 Mayo Street,Lexington',

        ]);

        DB::table('suppliers')->insert([
            'first_name' => '<PERSON>',
            'last_name' => 'Delos',
            'email' => '<EMAIL>',
            'company' => 'Spencer LLC',
            'phone_number' => '35793577',
            'address'=>'1032 Lady Bug Drive,Garden City',

        ]);
    }
}
