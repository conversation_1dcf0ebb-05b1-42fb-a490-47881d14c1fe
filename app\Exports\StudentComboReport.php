<?php

namespace App\Exports;

use App\Models\Customer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;


class StudentComboReport implements WithHeadings, FromQuery, WithMapping, WithEvents
//, WithChunkReading
{
    use Exportable;

    public $filterData;
    public $searchValue;

    function __construct($filterData, $searchValue){
        $this->filterData = $filterData; 
        $this->searchValue = $searchValue;
    }

    public function query()
    {
        
        $_searchValue = $this->searchValue;
        $_filterData  = $this->filterData;

        $query = Customer::query()
            ->leftJoin('customer_groups', 'customers.customer_group', '=', 'customer_groups.id')
            ->leftJoin('customer_payments', 'customer_payments.customer_id', '=', 'customers.id')
            ->leftJoin('product_courses', 'product_courses.id', '=', 'customers.course_id')
            ->leftJoin('product_packages', function ($join) {
                $join->on('product_packages.course_id', '=', 'product_courses.id')
                    ->on('product_packages.package_for', '=', 'customers.gender_id');
            })
            ->leftJoin('product_package_products', 'product_package_products.package_id', '=', 'product_packages.id')
            ->leftJoin('products', 'products.id', '=', 'product_package_products.product_id')
            ->leftJoin(DB::raw("(SELECT orders.customer_id, order_items.product_id, ABS(SUM(order_items.quantity)) AS issued_quantity 
                                FROM order_items 
                                LEFT JOIN orders ON orders.id = order_items.order_id 
                                WHERE orders.status = 'done' AND orders.order_type = 'sales' AND orders.customer_id IS NOT NULL 
                                GROUP BY order_items.product_id, orders.customer_id) AS rec"), function ($join) {
                $join->on('rec.product_id', '=', 'product_package_products.product_id')
                    ->on('rec.customer_id', '=', 'customers.id');
            })

            ->leftJoin(DB::raw("(SELECT customers.id as customer_id , order_items.product_id, '1' as is_jeans from  orders left join order_items on order_items.order_id = orders.id 
            left join customers on customers.id = orders.customer_id and customers.gender_id = 'F'
            WHERE order_items.product_id IN (62,64) and orders.customer_id IS NOT NULL AND orders.status = 'done'
            GROUP BY orders.customer_id) AS cust62t_64j"), function ($join) {
                $join->on('cust62t_64j.customer_id', '=', 'customers.id');
            })

            ->leftJoin(DB::raw("(SELECT customers.id as customer_id , order_items.product_id, '1' as is_kurti from  orders left join order_items on order_items.order_id = orders.id 
            left join customers on customers.id = orders.customer_id and customers.gender_id = 'F'
            WHERE order_items.product_id IN (76,82) and orders.customer_id IS NOT NULL AND orders.status = 'done'
            GROUP BY orders.customer_id) AS cust76k_82l"), function ($join) {
                $join->on('cust76k_82l.customer_id', '=', 'customers.id');
            })

            ->select(
                'customers.id',
                'customers.first_name',
                'customers.last_name',
                DB::raw("CONCAT(customers.first_name, ' ', customers.last_name) AS full_name"),
                'customers.email',
                'customers.roll_no',
                'customers.aadhr_card_no',
                'customers.company',
                'customers.gender_id',
                'customers.phone_number',
                'product_courses.course_name',
                'product_packages.package_name',
                'products.title',
                'product_package_products.product_id',
                'product_package_products.quantity',
                DB::raw("IFNULL(rec.issued_quantity, 0) AS issued_quantity"),
                DB::raw("(product_package_products.quantity - IFNULL(rec.issued_quantity, 0)) AS pending_quantity"),
                'cust62t_64j.is_jeans','cust76k_82l.is_kurti'
            )
            ->whereRaw("(product_package_products.quantity - IFNULL(rec.issued_quantity, 0)) > 0 AND customers.customer_group = 1  ")
            ->whereNotNull('customers.course_id');


        if ($_searchValue) {
            $query->where(function ($query) use ($_searchValue) {
                $query->where('customers.first_name', 'LIKE', '%' . $_searchValue . '%')
                    ->orWhere('customers.last_name', 'LIKE', '%' . $_searchValue . '%')
                    ->orWhere('customers.email', 'LIKE', '%' . $_searchValue . '%')
                    ->orWhere('customers.roll_no', 'LIKE', '%' . $_searchValue . '%')
                    ->orWhere('customers.aadhr_card_no', 'LIKE', '%' . $_searchValue . '%');
            });
        }

        if (!empty($_filterData)) {
            foreach ($_filterData as $singleFilter) {
                if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "gender") {
                    $query->where('customers.gender_id', $singleFilter['value']);
                }
                if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "course") {
                    $query->where('customers.course_id', $singleFilter['value']);
                }
                if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "product_name") {
                    $query->where('products.id', $singleFilter['value']);
                }
            }
        } 
        return $query;
    }

    public function map($reportRow): array
    {

        if ($reportRow->is_jeans == '1' &&  $reportRow->is_kurti == '1'){
            $sql  = "SELECT 
                        CASE    WHEN order_items.product_id IN (62, 64) THEN 'jeans' 
                                WHEN order_items.product_id IN (76, 82) THEN 'kurti' 
                        END AS product_group,
                        SUM(order_items.quantity) AS total_quantity
                    FROM  orders 
                    LEFT JOIN  order_items ON order_items.order_id = orders.id 
                    LEFT JOIN  customers ON customers.id = orders.customer_id AND customers.gender_id = 'F' 
                    WHERE  order_items.product_id IN (62, 64, 76, 82) AND orders.customer_id IS NOT NULL  AND orders.status = 'done' 
                        AND orders.customer_id = $reportRow->id 
                    GROUP BY  product_group
                    HAVING total_quantity < 0;";
            $results = DB::select($sql);
            $get_active_products = isset($results[0]) ? $results[0] : [];                    
            $products_group = isset($get_active_products->product_group) ? $get_active_products->product_group : '';
            if($products_group == 'kurti'){
                $reportRow->is_jeans = null;
                $reportRow->is_kurti = 1;
            }else{                        
                $reportRow->is_jeans = 1;
                $reportRow->is_kurti = null;                        
            }
        }                
        if($reportRow->is_jeans == '1' && $reportRow->is_kurti != '1'){
            if(in_array($reportRow->product_id, [76,82] ) ){
                #skip
               
            }else{
                return [
                    $reportRow->full_name,
                    $reportRow->roll_no,
                    $reportRow->company,
                    $reportRow->course_name,
                    $reportRow->gender_id,
                    $reportRow->email,
                    $reportRow->phone_number, 
                    $reportRow->title, 
                    $reportRow->pending_quantity,
                ]; 
            }
        }
        else if($reportRow->is_kurti == '1' && $reportRow->is_jeans != '1'){
            if(in_array($reportRow->product_id, [62,64] ) ){
                #skip
                  
            }else{
                return [
                    $reportRow->full_name,
                    $reportRow->roll_no,
                    $reportRow->company,
                    $reportRow->course_name,
                    $reportRow->gender_id,
                    $reportRow->email,
                    $reportRow->phone_number, 
                    $reportRow->title, 
                    $reportRow->pending_quantity,
                ]; 
            }
        }
        else if ($reportRow->is_jeans != '1' &&  $reportRow->is_kurti != '1'){
            return [
                $reportRow->full_name,
                $reportRow->roll_no,
                $reportRow->company,
                $reportRow->course_name,
                $reportRow->gender_id,
                $reportRow->email,
                $reportRow->phone_number, 
                $reportRow->title, 
                $reportRow->pending_quantity,
            ]; 
        }
 

        // -----------------------------------
        // if($reportRow->is_jeans == '1' ){
        //     if(in_array($reportRow->product_id, [76,82] ) ){
        //         #skip
        //     }else{
        //         return [
        //             $reportRow->full_name,
        //             $reportRow->roll_no,
        //             $reportRow->company,
        //             $reportRow->course_name,
        //             $reportRow->gender_id,
        //             $reportRow->email,
        //             $reportRow->phone_number, 
        //             $reportRow->title, 
        //             $reportRow->pending_quantity,
        //         ]; 
        //     }
        // }

        // if($reportRow->is_jeans != '1' ){
        //     if(in_array($reportRow->product_id, [62,64] ) ){
        //         #skip
        //     }else{
        //         return [
        //             $reportRow->full_name,
        //             $reportRow->roll_no,
        //             $reportRow->company,
        //             $reportRow->course_name,
        //             $reportRow->gender_id,
        //             $reportRow->email,
        //             $reportRow->phone_number, 
        //             $reportRow->title, 
        //             $reportRow->pending_quantity,
        //         ]; 
        //     }
        // }  


            return [
                
            ]; 
    }

    public function headings(): array
    {
        return [
            Lang::get('lang.name'),
            Lang::get('lang.roll_no'),
            Lang::get('lang.school_college'),
            Lang::get('lang.course'),
            Lang::get('lang.gender'),
            Lang::get('lang.customer_email'),
            Lang::get('lang.phone_number_datatable'),
            Lang::get('lang.product'),
            Lang::get('lang.pending_quantity')
        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:I1')->applyFromArray(
                    [
                        'font' => [
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' => [
                                'color' => ['argb' => 'FFFF0000']
                            ],
                        ]
                    ],
                );
            },
        ];
    }

    // public function chunkSize(): int
    // {
    //     return 1000;
    // }

}
