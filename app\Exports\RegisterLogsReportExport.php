<?php

namespace App\Exports;

use App\Libraries\AllSettingFormat;
use App\Models\CashRegisterLog;
use App\Models\CustomUser;
use App\Models\Payments;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Lang;

class RegisterLogsReportExport implements WithHeadings, FromQuery, WithMapping, WithEvents
{
    use Exportable;

    public function query()
    {

        $registerLogs = CashRegisterLog::join('users', 'cash_register_logs.user_id', '=', 'users.id')
            ->join('cash_registers', 'cash_register_logs.cash_register_id', '=', 'cash_registers.id')
            ->select(
                'cash_register_logs.*',
                'cash_registers.branch_id',
                'cash_registers.title',
                'cash_register_logs.opening_amount',
                'cash_register_logs.closing_amount',
                'cash_register_logs.expense_amount',
                'cash_register_logs.closed_by as closed_user',
                DB::raw(" cash_register_logs.closing_amount AS cash_register_closing_amount"),
                DB::raw(" cash_register_logs.opening_amount AS cash_register_opening_amount"),
                DB::raw("CONCAT(users.first_name,' ',users.last_name)  AS opened_by"),
                DB::raw('abs(cash_register_logs.opening_amount - cash_register_logs.closing_amount ) as difference')
            )
            ->orderBy('cash_registers.branch_id', 'desc');

        $logs = $registerLogs->get();

        foreach ($logs as $registerLog) {

            $paymentInfo = Payments::getPaymentInfo($registerLog->cash_register_id, $registerLog->opening_time, $registerLog->closing_time);

            $registerLog->cash_receives = $paymentInfo['receiving'];
            $registerLog->cash_sales = $paymentInfo['sales'];


            if ($registerLog->difference == null) {
                $registerLog->difference = '';
            }

            if ($registerLog->closing_amount == null) {
                $registerLog->closing_amount = '';
            }

            if ($registerLog->closed_by) {
                //$registerLog->difference = (float)$registerLog->opening_amount + (float)$registerLog->cash_sales - (float)$registerLog->cash_receives - (float)$registerLog->closing_amount - (float)$registerLog->expense_amount;
                $registerLog->difference = (float) $registerLog->opening_amount + (float) $registerLog->cash_sales - (float) $registerLog->closing_amount - (float) $registerLog->expense_amount;
                $registerLog->closed_user = CustomUser::userInfo($registerLog->closed_by);
            }

            if ($registerLog->status == 'closed') {
                $registerLog->status = Lang::get('lang.closed');
            } else {
                $registerLog->status = Lang::get('lang.open');
                $registerLog->closing_amount = '';
            }
        }

        return $registerLogs;
    }

    public function map($reportRow): array
    {
        $allSettingFormat = new AllSettingFormat;
        return
            [
                $reportRow->branch_id,
                $reportRow->title,
                $reportRow->opened_by,
                $reportRow->closed_by,
                ucwords($reportRow->status),
                $reportRow->note,
                $allSettingFormat->getCurrencySeparator($reportRow->opening_amount),
                $allSettingFormat->getCurrencySeparator($reportRow->cash_sales),
                $reportRow->expense_amount,
                $reportRow->closing_amount,
                $reportRow->difference,
            ];
    }

    public function headings(): array
    {
        return [
                "ID",
                "Title",
                "Opened By",
                "Closed By",
                "Status",
                "Note",
                "Opening Amount",
                "Cash Sales",
                "Expenses",
                "Closing Amount",
                "Difference"
        ];        
    }

    //Highlight heading column
    public function registerEvents(): array
    {

        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:K1')->applyFromArray(
                    [
                        'font' => [
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' => [
                                'color' => ['argb' => 'FFFF0000']
                            ],
                        ]
                    ],
                );
            },
        ];
    }

}
