.tag {
  display: inline-block;
  text-align: center;
  padding: 0.4rem 0.6rem;
  margin: 0 0.5rem 0.5rem 0;
  box-shadow: 0 2px 10px 1px rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  color: #ffffff;
  font-size: 0.8rem;
  white-space: nowrap;
  line-height: 1;
  font-weight: 100; }
  .tag span {
    padding: 0rem .6rem; }
  .tag i.cancel {
    padding: .25em;
    color: #ffffff;
    cursor: pointer;
    opacity: 0.6; }
  .tag:hover i.cancel {
    opacity: 1; }
  .tag:last-child {
    margin: 0; }

.tag-pill {
  border-radius: 10rem; }

.tag-primary {
  background-color: #4a97fd;
  color: #ffffff; }
  .tag-primary:hover {
    background-color: #4a97fd;
    color: #ffffff; }

.tag-secondary {
  background-color: #a1a5ac;
  color: #ffffff; }
  .tag-secondary:hover {
    background-color: #a1a5ac;
    color: #ffffff; }

.tag-success {
  background-color: #63b870;
  color: #ffffff; }
  .tag-success:hover {
    background-color: #63b870;
    color: #ffffff; }

.tag-danger {
  background-color: #e46370;
  color: #ffffff; }
  .tag-danger:hover {
    background-color: #e46370;
    color: #ffffff; }

.tag-warning {
  background-color: #ffcd4d;
  color: #ffffff; }
  .tag-warning:hover {
    background-color: #ffcd4d;
    color: #ffffff; }

.tag-info {
  background-color: #5bb5c6;
  color: #ffffff; }
  .tag-info:hover {
    background-color: #5bb5c6;
    color: #ffffff; }

.tag-light {
  background-color: #ffffff;
  color: #999999;
  box-shadow: 0 2px 10px 1px rgba(0, 0, 0, 0.04); }
  .tag-light:hover {
    background-color: #ffffff;
    color: #999999; }

.tag-dark {
  background-color: #343a40;
  color: #ffffff; }
  .tag-dark:hover {
    background-color: #343a40;
    color: #ffffff; }

/*# sourceMappingURL=tags.css.map */
