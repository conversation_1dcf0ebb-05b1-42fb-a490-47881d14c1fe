<?php

namespace App\Http\Controllers\API;

use App\Models\CustomerPayment;
use App\Models\Product;
use App\Models\ProductCourse;
use App\Models\ProductPackage;
use App\Models\Customer;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;

class ProductPackageController extends Controller
{

    public function getPackages(Request $request)
    {
        // DB::enableQueryLog();
        $ProductPackages = ProductPackage::getPackagesData($request); 
   
        // dd(DB::getQueryLog());

      

        return ['datarows' => $ProductPackages['data'], 'count' => $ProductPackages['count']];
    }

     

    public function store(Request $request)
    { 
        $validator = Validator::make($request->all(), [
            'courseName' => 'required|unique:product_packages,package_name', 
            'courseId' => 'required',
            'productList' => 'required',            
        ],
        [
            'courseName.required' => 'The Course is required.',
            'courseName.unique' => 'The Course is already exist.', 
            'courseId' => 'The Course is required.',
        ]);
        
        if ($validator->fails()) {
            $response = [
                'message' => $validator->errors()->first()
            ];
            return response()->json($response, 404);
        }

        $productList = $request->input('productList'); 

        if ($productPackageID = ProductPackage::store([
            'package_name'  => $request->input('courseName'), 
            'course_id'     => $request->input('courseId'), 
            'package_for'   => $request->input('packageFor'),
            'created_by'    => Auth::user()->id,
        ])) {

            $products = [];
            if(!empty($productList)){
                foreach($productList as $key => $rec){
                    $_product = [
                        'product_id'    => $rec['id'],
                        'quantity'      => $rec['qty'],
                        'package_id'    => $productPackageID->id,
                    ];
                    $products[] = $_product;
                }
                DB::table('product_package_products')->insert($products);
            }
            

            $response = [
                'message' => Lang::get('lang.package') . ' ' . Lang::get('lang.successfully_saved')
            ];

            return response()->json($response, 201);
        } else {
            $response = [
                'message' => Lang::get('lang.getting_problems')
            ];

            return response()->json($response, 404);
        }
    }

    public function show($id)
    {
       $response['package'] = ProductPackage::getOne($id);
       $response['course'] = ProductCourse::getOne($response['package']->course_id);
       $response['package_products'] = \DB::table('product_package_products')
        ->join('products', 'products.id', 'product_package_products.product_id')
        ->select('products.id', 'product_package_products.quantity as qty', 'products.title as name', 'product_package_products.id as rec_id')
       ->where('product_package_products.package_id', $id)->get();
       $product_ids = [];
       if(!empty($response['package_products'])){
        foreach($response['package_products'] as $key => $rec){
            $product_ids[] = $rec->id;
        }
       }

       $response['product_ids'] = $product_ids;
       return response()->json($response, 201);
    }

    public function update(Request $request, $id)
    { 
        $validator = Validator::make($request->all(), [          
            'courseId' => 'required',
            'productList' => 'required',            
        ],
        [
            'courseName.unique' => 'The Course is already exist.', 
            'courseId' => 'The Course is required.',
        ]);
        
        if ($validator->fails()) {
            $response = [
                'message' => $validator->errors()->first()
            ];
            return response()->json($response, 404);
        }

        $productList = $request->input('productList');
        
        $productIds  = [];
        if(!empty($productList)){
             
            foreach($productList as$key => $rec){
                $rec_id = isset($rec['rec_id']) ? $rec['rec_id'] : 0;
                
                $productIds[] = $rec['id'];

                if($rec_id){
                    #update
                    DB::table('product_package_products')
                        ->where('package_id', $id)
                        ->where('id', $rec_id)
                        ->update(['quantity' =>  $rec['qty'] ]);
                }else{
                    $_product = [
                        'product_id'    => $rec['id'],
                        'quantity'      => $rec['qty'],
                        'package_id'    => $id,
                    ];
                    DB::table('product_package_products')->insert($_product);
                }
            }
        }
        

        #delete other 
        if(!empty($productIds)) {
            DB::table('product_package_products')
                ->where('package_id', $id)  // Assuming $id is defined elsewhere in your code
                ->whereNotIn('product_id', $productIds)
                ->delete(); // Delete the records
        }
        

        $response = [
            'message' => Lang::get('lang.package') . ' ' . Lang::get('lang.successfully_saved')
        ];

        return response()->json($response, 201);
        
    }

    public function delete($id)
    {
        // $used = Product::countRecord('unit_id', $id);

        // if ($used == 0) {
            ProductPackage::deleteData($id);
            $response = [
                'message' => Lang::get('lang.course') . ' ' . Lang::get('lang.successfully_deleted')
            ];

            return response()->json($response, 201);
        // } else {
        //     $response = [
        //         'message' => Lang::get('lang.course').' '.Lang::get('lang.in_use').', '.Lang::get('lang.you_can_not_delete_the').' '.strtolower(Lang::get('lang.unit'))

        //     ];

        //     return response()->json($response, 200);
        // }
    }

    public function getStudentPackage($id){
        $customer   =   Customer::customerDetails($id);
        $getPackage =   ProductPackage::getPackageDetails($customer);
        $courseInfo =   ProductCourse::getOne($customer['course_id']);

        // $customer_roll_no = Customer::where("id", $customer->id)->pluck('roll_no');
        // Find the latest record by roll_no
        $latestPayment = CustomerPayment::where('customer_id', $customer->id)
                                        ->latest()
                                        ->first();
        
        $customer['latest_payment'] = $latestPayment;
        $productIds = [];
        if(!empty($getPackage)){
            foreach($getPackage as $key => $rec){
                $productIds[] = $rec->product_id;
            }
        } 

        $customerQuantities = ProductPackage::getCustomerProductsQty($productIds, $id, 'combo');
  

        #create array
        $_getPackage = [];
        if(!empty($getPackage)){
            foreach($getPackage as $key => $rec){            
                $_getPackage[$key] = (array)$rec;
                $_getPackage[$key]['issued']    = 0;
                $_getPackage[$key]['issuing']   = 0;
                $_getPackage[$key]['balance']   = $rec->quantity;                
                $_getPackage[$key]['a_qty']     = $rec->quantity;
                $_getPackage[$key]['a_issuing'] = 0;

                if(!empty($customerQuantities)){
                    foreach($customerQuantities as $k => $rec1){
                        if($rec1->product_id == $rec->product_id){
                            $_getPackage[$key]['issued'] = abs($rec1->total_quantity);
                            $_getPackage[$key]['balance'] = $rec->quantity - abs($rec1->total_quantity);      
                        }
                    }
                } 
            }
        }

        $response['customer']   =   $customer;
        $response['package']    =   $_getPackage;
        $response['course']     =   $courseInfo;

        // if(!empty($_getPackage)){
            return response()->json($response, 201);
        // }else{
        //     $response = [
        //         'message' => Lang::get('lang.getting_problems')
        //     ];

        //     return response()->json($response, 404);
        // } 

    }
   
}
