<?php

namespace App\Models;
use DB;

class Supplier extends BaseModel
{
    protected $fillable = ['last_name','last_name'];

    public static function getSuppliers($column, $request, $limit, $rowOffset, $requestType, $searchValue, $filtersData)
    {
        $query = Supplier::select('id', DB::raw('CONCAT(first_name," ", last_name) AS name'), 'email', 'company', 'phone_number', 'address', 'tin_number', 'type', DB::raw('DATE_FORMAT(created_at, "%d/%m/%Y") AS date'));

        if(!empty($searchValue)) {
            $query->where('first_name', 'LIKE', '%'.$searchValue.'%');
            $query->orWhere('last_name', 'LIKE', '%'.$searchValue.'%');
            $query->orWhere('email', 'LIKE', '%'.$searchValue.'%');
            $query->orWhere('company', 'LIKE', '%'.$searchValue.'%');
            $query->orWhere('type', 'LIKE', '%'.$searchValue.'%');
            $query->orWhere('phone_number', 'LIKE', '%'.$searchValue.'%');
            $query->orWhere('address', 'LIKE', '%'.$searchValue.'%');
        }

        if(isset($filtersData)) {
            foreach ($filtersData as $singleFilter) {
                if (array_key_exists('filterKey', $singleFilter) && $singleFilter['filterKey'] == "date_range") {
                    $query->where('created_at', '>=', $singleFilter['value'][0]['start']." 00:00:00")
                        ->where('created_at', '<=', $singleFilter['value'][0]['end']." 23:59:59");
                } else if (array_key_exists('key', $singleFilter)) {
                    $query->where('id', $singleFilter['value']);
                }
            }
        }

        $count = $query->count();
        if (empty($requestType)) {
            $data = $query->orderBy($column, $request)->take($limit)->skip($rowOffset)->get();

        } else {
            $data = $query->orderBy($column, $request)->get();
        }

        return ['data' => $data, 'count' => $count];
    }

    public static function getSalesusers($column, $request, $limit, $rowOffset, $requestType, $searchValue, $filtersData)
    {
        $query = DB::table('inventory_sales_users')->select('id', DB::raw('CONCAT(first_name," ", last_name) AS name'), 'email',  'phone_number', 'address');
        $query->where('status', '1');

        if(!empty($searchValue)) {
            $query->orWhere('first_name', 'LIKE', '%'.$searchValue.'%');
            $query->orWhere('last_name', 'LIKE', '%'.$searchValue.'%');
            $query->orWhere('email', 'LIKE', '%'.$searchValue.'%'); 
            $query->orWhere('phone_number', 'LIKE', '%'.$searchValue.'%');
            $query->orWhere('address', 'LIKE', '%'.$searchValue.'%');
        } 

        $count = $query->count();
        if (empty($requestType)) {
            $data = $query->orderBy($column, $request)->take($limit)->skip($rowOffset)->get();

        } else {
            $data = $query->orderBy($column, $request)->get();
        }

        return ['data' => $data, 'count' => $count];
    }

    
    public static function insertSalesUser($data){
        return DB::table('inventory_sales_users')->insertGetId($data);
    }
    public static function updateSalesUser($data, $id){
        return DB::table('inventory_sales_users')
                ->where('id', $id)
                ->update($data);
    }

    public static function getSalesUser($id){
        return DB::table('inventory_sales_users')
                ->where('id', $id)->first();
    } 

    

    public static function supplierData($searchValue)
    {
        $query = Supplier::select('id','first_name','last_name','email','company','phone_number','address');

        if ($searchValue) {
            $query->where(function ($query) use ($searchValue) {
                $query->where('first_name', 'LIKE', '%' . $searchValue . '%')
                    ->orWhere('last_name', 'LIKE', '%' . $searchValue . '%')
                    ->orWhere('email', 'LIKE', '%' . $searchValue . '%');
            });
        }

        return $query->orderBy('id', 'DESC')->get();
    }

    public static function getAllEmails()
    {
        return Supplier::select('email')->get();
    }
}
