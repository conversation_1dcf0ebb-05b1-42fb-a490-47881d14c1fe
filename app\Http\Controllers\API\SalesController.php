<?php

namespace App\Http\Controllers\API;

use App\Helpers\FileHandler;
use App\Http\Controllers\API\Sales\Traits\SaleHelper;
use App\Libraries\AllSettingFormat;
use App\Libraries\SmsHelper;
use App\Libraries\Permissions;
use App\Libraries\Email;
use App\Libraries\searchHelper;
use App\Models\Branch;
use App\Models\CashRegister;
use App\Models\CashRegisterLog;
use App\Models\CustomerGroup;
use App\Models\EmailTemplate;
use App\Models\InvoiceTemplate;
use App\Models\Order;
use App\Models\Setting;
use App\Models\OrderItems;
use App\Models\Payments;
use App\Models\Product;
use App\Models\ProductAttribute;
use App\Models\ProductAttributeValue;
use App\Models\ProductVariant;
use App\Models\ShortcutKey;
use App\Models\Tax;
use App\Models\Customer;
use App\Models\CustomUser;
use App\Models\Supplier;
use App\Models\RestaurantTable;
use App\Models\SmsTemplate;

use App\Models\ShippingInformation;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use PDF;
use Config;
use Milon\Barcode\DNS1D;
use DB;
use App\Models\PurchaseQuotation;   
use Illuminate\Support\Facades\Log;
use App\Libraries\imageHandler;
use App\Models\CustomerPayment;

class SalesController extends Controller
{
    use SaleHelper, FileHandler;

    private $paymentController;

    public function __construct()
    {
        $this->paymentController = new PaymentController;
    }

    public function permissionCheck()
    {
        return new Permissions;
    }

    public function salePersonsList(){
        $salesPersons = CustomUser::staffList();
        return $salesPersons;
    }

    public function salesView()
    {
        $allSettings = new AllSettingFormat;
        $BranchController = new BranchController;
        $getBranch = $BranchController->index();
        $totalBranch = sizeof($getBranch);

        $paymentTypes = $this->paymentController->getData();
        $autoInvoice = $this->paymentController->getAutoInvoice();
        // $customer = Customer::getCustomerDetails();
        $customer = Customer::getCustomerDetailsDuplicate();
        
        

        $customerGroup = CustomerGroup::allData();
        $cashRegisterID = $this->getCashRegisterID();
        $salesReturnStatus = Setting::getSettingValue('sales_return_status')->setting_value;
        $salesType = Setting::getSaleOrReceivingType('sales_type');
        $invoiceData = $this->invoiceData();
        $userID = auth()->id();
        $currentBranch = Setting::currentBranch($userID);
        $holdOrders = $this->getHoldOrder();
        $restaurantTables = RestaurantTable::all();

        $defaultInvoiceTemplate = InvoiceTemplate::getDefaultTemplate();
        $bookedTables = Order::getBookedTables();
        $output = [
            'currentBranch' => $allSettings->getCurrentBranch(),
            'totalBranch' => $totalBranch,
            'currentCashRegister' => $cashRegisterID,
            'salesReturnStatus' => $salesReturnStatus,
            'salesType' => $salesType,
            'branches' => $getBranch,
            'autoInvoice' => $autoInvoice['autoInvoice'],
            'paymentTypes' => $paymentTypes,
            'customer' => $customer,            
            'customerGroup' => $customerGroup,
            'invoicePrefix' => $invoiceData['prefix'],
            'invoiceSuffix' => $invoiceData['suffix'],
            'lastInvoiceNum' => $invoiceData['lastInvoiceNum'],
            'appName' => '',
            'isBranchSelected' => false,
            'product' => null,
            'shortcutKeyCollection' => null,
            'holdOrders' => $holdOrders,
            'defaultInvoiceTemplateForSales' => $defaultInvoiceTemplate['sales_invoice']['invoice_template'],
            'restaurantTables' => $restaurantTables,
            'bookedTables' => $bookedTables,
        ];


        if ($currentBranch != null) {
//            $product = $this->getProduct('sales', $currentBranch->setting_value);
            $output['isBranchSelected'] = true;
//            $output['product'] = null;
//            $output['shortcutKeyCollection'] = $product['shortcutKeyCollection'];
        }
        return view('sales.SalesIndex', $output);
    }

    public function invoiceData()
    {
        $invoicePrefix = Setting::getSettingValue('invoice_prefix')->setting_value;
        $invoiceSuffix = Setting::getSettingValue('invoice_suffix')->setting_value;
        $lastInvoiceNum = Setting::getSettingValue('last_invoice_number')->setting_value;
        $grnInvoicePrefix = Setting::getSettingValue('grn_invoice_prefix')->setting_value;
        $grnInvoiceSuffix = Setting::getSettingValue('grn_invoice_suffix')->setting_value;
        $grnLastInvoiceNum = Setting::getSettingValue('grn_last_invoice_number')->setting_value;

        return ['prefix' => $invoicePrefix, 'suffix' => $invoiceSuffix, 'lastInvoiceNum' => $lastInvoiceNum, 'grnInvoicePrefix' => $grnInvoicePrefix, 'grnInvoiceSuffix' => $grnInvoiceSuffix,  'grnLastInvoiceNum' => $grnLastInvoiceNum];
    }

    public function getRegisterAmount($id)
    {
        //return CashRegisterLog::getRegisterAmount($id);
  
        $log = CashRegisterLog::where('cash_register_id', $id)->where('status', 'open')->first();
        $start = $log->opening_time;

        $totalSales             = Payments::join('orders', 'payments.order_id', 'orders.id')->where('orders.order_type', 'sales')->where('orders.status', 'done')->where('payments.cash_register_id', $id)->where('payments.created_at','>=',$start)->sum('payments.paid');
        $totalReceiving         = Payments::join('orders', 'payments.order_id', 'orders.id')->where('orders.order_type', 'receiving')->where('orders.status', 'done')->where('payments.cash_register_id', $id)->where('payments.created_at','>=',$start)->sum('payments.paid');
        $paymentcashType        = $this->paymentController->getData()->where('type', 'cash')->first();  
        
        $paymentcashTypeId      = isset($paymentcashType->id) ? $paymentcashType->id  : 1;

        $totalCashSales         = Payments::join('orders', 'payments.order_id', 'orders.id')->where('orders.order_type', 'sales')->where('orders.status', 'done')->where('payments.cash_register_id', $id)->where('payments.created_at','>=',$start)->where('payments.payment_method',$paymentcashTypeId)->sum('payments.paid');

        $total                  =   $log->opening_amount + $totalSales - $totalReceiving;
        $expected_closing_cash  =   $log->opening_amount + $totalCashSales;

    
        $totalSales = Payments::select(\DB::raw('SUM(payments.paid) as paid'), 'payments.payment_method', 'payment_types.name')
        ->join('orders', 'payments.order_id', 'orders.id')
        ->join('payment_types', 'payment_types.id', 'payments.payment_method')
        ->where('orders.order_type', 'sales')->where('orders.status', 'done')
        ->where('payments.cash_register_id', $id)->where('payments.created_at','>=',$start)->groupBy('payments.payment_method')->get();

       
        $response['opening_amount'] = $log->opening_amount;
        $response['total'] = $total;
        $response['expected_closing_amount'] = $expected_closing_cash;
        $response['payments'] = $totalSales;

        return $response;
    }

    public function getCashRegisterID()
    {
        $userID = Auth::user()->id;
        $currentBranch = Setting::currentBranch($userID);
        $currentBranchID = 0;

        if ($currentBranch) {
            $currentBranchID = $currentBranch->setting_value;
        }

        $cashRegisterID = CashRegisterLog::getRegistersLog($currentBranchID, $userID);

        if ($cashRegisterID) {
            return $cashRegisterID = CashRegister::getCashRegisters($cashRegisterID->cash_register_id);
        } else {
            return $cashRegisterID = null;
        }
    }

    public function purchaseView()
    {
        $allSettings = new AllSettingFormat;
        $BranchController = new BranchController;
        $getBranch = $BranchController->index();
        $totalBranch = sizeof($getBranch);

        $paymentTypes = $this->paymentController->getData();        
        $autoInvoice = $this->paymentController->getAutoInvoice();
        $supplier = Supplier::all();
        
        $cashRegisterID = $this->getCashRegisterID();
        $receivingType = Setting::getSaleOrReceivingType('receiving_type');
        // \DB::enableQueryLog();
        $invoiceData = $this->invoiceData();
        $userID = Auth::user()->id;
        
        $currentBranch = Setting::currentBranch($userID);
        $defaultInvoiceTemplate = InvoiceTemplate::getDefaultTemplate();
        $purchaseReturnStatus = Setting::getSettingValue('purchase_return_status')->setting_value;
        $output = [
            'currentBranch' => $allSettings->getCurrentBranch(),
            'totalBranch' => $totalBranch,
            'currentCashRegister' => $cashRegisterID,
            'receivingType' => $receivingType,
            'branches' => $getBranch,
            'paymentTypes' => $paymentTypes,
            'purchaseReturnStatus' => $purchaseReturnStatus,
            'autoInvoice' => $autoInvoice['autoInvoice'],
            'supplier' => $supplier,
            'invoicePrefix' => $invoiceData['prefix'],
            'invoiceSuffix' => $invoiceData['suffix'],
            'lastInvoiceNum' => $invoiceData['lastInvoiceNum'],
            'appName' => '',
            'isBranchSelected' => false,
            'product' => null,
            'shortcutKeyCollection' => null,
            'defaultInvoiceTemplateForReceives' => $defaultInvoiceTemplate['receive_invoice']['invoice_template']
        ];
        if ($currentBranch != null) {
//            $product = $this->getProduct('receiving', $currentBranch->setting_value);
            $output['isBranchSelected'] = true;
//            $output['product'] = $product['products'];
//            $output['shortcutKeyCollection'] = $product['shortcutKeyCollection'];
        }
        return view('receives.ReceivesIndex', $output);
    }

    public function getReturnProduct(Request $request)
    {
        $orderId = $request->orderId;
        $receivingType = $request->receivingType;
        $orderType = $receivingType ? 'receiving' : 'sales';

        Log::info("\n".'----------------Search Return Orders API------------------');
        Log::info("Request Data", [$request]);

        // \DB::enableQueryLog();
        $orderItems = Order::searchOrders($orderId, $orderType);
        // dd(\DB::getQueryLog());

        Log::info("orderItems", [$orderItems]);
        
        if(!empty($orderItems)){
            foreach ($orderItems as $rowOrderItem) {
                $rowOrderItem->cart = OrderItems::getOrderItemsDetails($rowOrderItem->orderID);
                if(!empty($rowOrderItem) && isset($rowOrderItem->cart[0])){
                    // \DB::enableQueryLog();
                    $getReturnProduct = Order::getReturnProduct($rowOrderItem->cart[0]['invoiceReturnId'], $orderType);
                    // dd(\DB::getQueryLog());
                    $varientAvailableQty = OrderItems::productQuantity($rowOrderItem->cart[0]['variantID']);
                    $rowOrderItem->cart[0]['availQty'] = $varientAvailableQty->quantity;// 20221102
                    $rowOrderItem->availQuantity = $varientAvailableQty->quantity;
                    if($varientAvailableQty->quantity > 0){
                        $rowOrderItem->payBtnEnable = 1;
                    }else{
                        $rowOrderItem->payBtnEnable = 0;
                    }
                    
                    $return_products = optional($getReturnProduct)->groupBy('variant_id')->map(function ($items) {
                        $item = $items->first();
                        $item->quantity = $items->sum('quantity');
                        return $item;
                    });
                    
                    $modified_cart = $rowOrderItem->cart->map(function ($cart) use ($return_products) {
                        $return_product = $return_products->where('variant_id', $cart->variantID)->first();
                        $cart->quantity = optional($cart)->quantity + optional($return_product)->quantity;
                        return $cart;
                    });
                    
                    // dd('$modified_cart: '.$modified_cart);
                    //$rowOrderItem->cart = OrderItems::getAll(['invoice_id','price', 'discount', 'product_id as productID', 'type as orderType', 'tax_id as taxID', 'quantity', 'variant_id as variantID', 'note as cartItemNote'], 'order_id', $rowOrderItem->orderID);
                    foreach ($modified_cart as $rowItem) {
                        if ($rowItem->taxID) {
                            $rowItem->productTaxPercentage = Tax::getFirst('percentage', 'id', $rowItem->taxID)->percentage;
                        } else {
                            $rowItem->productTaxPercentage = 0;
                        }
        
                        $rowItem->returnType = $rowOrderItem['return_type'];
        
                        if ($rowItem->variantID != null) {
                            $firstVariant = ProductVariant::getFirst('variant_title', 'id', $rowItem->variantID);
                            $rowItem->variantTitle = $firstVariant ? $firstVariant->variant_title : "";
        
                            $firstProductTitle = Product::getFirst('title', 'id', $rowItem->productID);
                            $rowItem->productTitle = $firstProductTitle ? $firstProductTitle->title : "";
                        }
        
                        $rowItem->showItemCollapse = false;
                        $rowItem->calculatedPrice = $rowItem->quantity * $rowItem->price;
                    }
        
                    if ($rowOrderItem->customer != null) {
                        $rowOrderItem->customer = Customer::getFirst(['first_name', 'last_name', 'email', 'id'], 'id', $rowOrderItem->customer);                        
                        if(!empty($rowOrderItem->customer))
                            $rowOrderItem->customer->customer_group_discount = 0;
                    }
        
                    if ($rowOrderItem->supplier_id != null) {
                        $rowOrderItem->customer = Supplier::getOne($rowOrderItem->supplier_id);
                        if(!empty($rowOrderItem->customer))
                            $rowOrderItem->customer->customer_group_discount = 0;
                    }
                }
            }
        }

        return $orderItems;
    }

    public function getPurchaseEditReturnProduct(Request $request)
    {
        $orderId = $request->orderId;
        $receivingType = $request->receivingType;
        $orderType = $receivingType ? 'receiving' : 'sales';
        $orderItems = Order::grnSearchOrders($orderId, $orderType);
                
        foreach ($orderItems as $rowOrderItem) {
            $rowOrderItem->cart = OrderItems::getPurchaseEditOrderItemsDetails($rowOrderItem->orderID);
            
        // \DB::enableQueryLog();
            $getReturnProduct = Order::getReturnProduct($rowOrderItem->cart[0]['invoiceReturnId'], $orderType);
        // dd(\DB::getQueryLog());
            $varientAvailableQty = OrderItems::productQuantity($rowOrderItem->cart[0]['variantID']);
           
            $rowOrderItem->cart[0]['availQty'] = $varientAvailableQty->quantity;// 20221102
            $rowOrderItem->availQuantity = $varientAvailableQty->quantity;
            if($varientAvailableQty->quantity > 0){
                $rowOrderItem->payBtnEnable = 1;
            }else{
                $rowOrderItem->payBtnEnable = 0;
            }
            
            $return_products = optional($getReturnProduct)->groupBy('variant_id')->map(function ($items) {
                $item = $items->first();
                $item->quantity = $items->sum('quantity');
                return $item;
            });
            
            $modified_cart = $rowOrderItem->cart->map(function ($cart) use ($return_products) {
                $return_product = $return_products->where('variant_id', $cart->variantID)->first();
                $cart->quantity = optional($cart)->quantity + optional($return_product)->quantity;
                return $cart;
            });
            
            // dd('$modified_cart: '.$modified_cart);
            //$rowOrderItem->cart = OrderItems::getAll(['invoice_id','price', 'discount', 'product_id as productID', 'type as orderType', 'tax_id as taxID', 'quantity', 'variant_id as variantID', 'note as cartItemNote'], 'order_id', $rowOrderItem->orderID);
            foreach ($modified_cart as $rowItem) {
                if ($rowItem->taxID) {
                    $rowItem->productTaxPercentage = Tax::getFirst('percentage', 'id', $rowItem->taxID)->percentage;
                } else {
                    $rowItem->productTaxPercentage = 0;
                }

                $rowItem->returnType = $rowOrderItem['return_type'];


                if ($rowItem->variantID != null) {
                    $firstVariant = ProductVariant::getFirst('variant_title', 'id', $rowItem->variantID);
                    $rowItem->variantTitle = $firstVariant ? $firstVariant->variant_title : "";

                    $firstProductTitle = Product::getFirst('title', 'id', $rowItem->productID);
                    $rowItem->productTitle = $firstProductTitle ? $firstProductTitle->title : "";
                }

                $rowItem->showItemCollapse = false;
                $rowItem->calculatedPrice = $rowItem->quantity * $rowItem->price;
            }

            if ($rowOrderItem->customer != null) {
                $rowOrderItem->customer = Customer::getFirst(['first_name', 'last_name', 'email', 'id'], 'id', $rowOrderItem->customer);
                $rowOrderItem->customer->customer_group_discount = 0;
            }

            if ($rowOrderItem->supplier_id != null) {
                $rowOrderItem->customer = Supplier::getOne($rowOrderItem->supplier_id);
                $rowOrderItem->customer->customer_group_discount = 0;
            }

            
        }

        return $orderItems;
    }


    public function getGRNOrder(Request $request)
    {
        $orderId = $request->orderId;
        $receivingType = $request->receivingType;
        $orderType = $receivingType ? 'receiving' : 'sales';
        $orderItems = Order::grnSearchOrders($orderId, $orderType);
               
        foreach ($orderItems as $rowOrderItem) {
            // \DB::enableQueryLog();
            $rowOrderItem->cart = OrderItems::getGRNOrderItemsDetails($rowOrderItem->invoice_id);
            //  dd(\DB::getQueryLog());
        
            $getReturnProduct = Order::getReturnProduct($rowOrderItem->cart[0]['invoiceReturnId'], $orderType);
            // dd($getReturnProduct);
       
            $varientAvailableQty = OrderItems::productQuantity($rowOrderItem->cart[0]['variantID']);
           
            $rowOrderItem->cart[0]['availQty'] = $varientAvailableQty->quantity;// 20221102
            $rowOrderItem->availQuantity = $varientAvailableQty->quantity;
            if($varientAvailableQty->quantity > 0){
                $rowOrderItem->payBtnEnable = 1;
            }else{
                $rowOrderItem->payBtnEnable = 0;
            }
            
            $return_products = optional($getReturnProduct)->groupBy('variant_id')->map(function ($items) {
                $item = $items->first();
                // $item->quantity = $items->sum('quantity');
                return $item;
            });
            
            $modified_cart = $rowOrderItem->cart->map(function ($cart) use ($return_products) {
                $return_product = $return_products->where('variant_id', $cart->variantID)->first();
                // $cart->quantity = optional($cart)->quantity + optional($return_product)->quantity;
                return $cart;
            });
            
            // dd('$modified_cart: '.$modified_cart);
            //$rowOrderItem->cart = OrderItems::getAll(['invoice_id','price', 'discount', 'product_id as productID', 'type as orderType', 'tax_id as taxID', 'quantity', 'variant_id as variantID', 'note as cartItemNote'], 'order_id', $rowOrderItem->orderID);
            foreach ($modified_cart as $rowItem) {
                if ($rowItem->taxID) {
                    $rowItem->productTaxPercentage = Tax::getFirst('percentage', 'id', $rowItem->taxID)->percentage;
                } else {
                    $rowItem->productTaxPercentage = 0;
                }

                $rowItem->returnType = $rowOrderItem['return_type'];


                if ($rowItem->variantID != null) {
                    $firstVariant = ProductVariant::getFirst('variant_title', 'id', $rowItem->variantID);
                    $rowItem->variantTitle = $firstVariant ? $firstVariant->variant_title : "";

                    $firstProductTitle = Product::getFirst('title', 'id', $rowItem->productID);
                    $rowItem->productTitle = $firstProductTitle ? $firstProductTitle->title : "";
                }

                $rowItem->showItemCollapse = false;
                $rowItem->calculatedPrice = $rowItem->quantity * $rowItem->price;
            }

            if ($rowOrderItem->customer != null) {
                $rowOrderItem->customer = Customer::getFirst(['first_name', 'last_name', 'email', 'id'], 'id', $rowOrderItem->customer);
                $rowOrderItem->customer->customer_group_discount = 0;
            }

            if ($rowOrderItem->supplier_id != null) {
                $rowOrderItem->customer = Supplier::getOne($rowOrderItem->supplier_id);
                $rowOrderItem->customer->customer_group_discount = 0;
            }

            
        }

        return $orderItems;
    }
    
    public function getQuotationOrderProducts(Request $request)
    {
        $orderId = $request->orderId;
        $receivingType = $request->receivingType;
        $orderType = $receivingType ? 'receiving' : 'sales';
        $orderItems = Order::quotationSearchOrders($orderId, $orderType);
                
        foreach ($orderItems as $rowOrderItem) {
            $rowOrderItem->cart = OrderItems::getQuotationOrderItemsDetails($rowOrderItem->orderID);
            
        // \DB::enableQueryLog();
            $getReturnProduct = Order::getReturnProduct($rowOrderItem->cart[0]['invoiceReturnId'], $orderType);
        // dd(\DB::getQueryLog());
            $varientAvailableQty = OrderItems::productQuantity($rowOrderItem->cart[0]['variantID']);
           
            $rowOrderItem->cart[0]['availQty'] = $varientAvailableQty->quantity;// 20221102
            $rowOrderItem->availQuantity = $varientAvailableQty->quantity;
            if($varientAvailableQty->quantity > 0){
                $rowOrderItem->payBtnEnable = 1;
            }else{
                $rowOrderItem->payBtnEnable = 0;
            }
            
            $return_products = optional($getReturnProduct)->groupBy('variant_id')->map(function ($items) {
                $item = $items->first();
                $item->quantity = $items->sum('quantity');
                return $item;
            });
            
            $modified_cart = $rowOrderItem->cart->map(function ($cart) use ($return_products) {
                $return_product = $return_products->where('variant_id', $cart->variantID)->first();
                $cart->quantity = optional($cart)->quantity + optional($return_product)->quantity;
                return $cart;
            });
            
            // dd('$modified_cart: '.$modified_cart);
            //$rowOrderItem->cart = OrderItems::getAll(['invoice_id','price', 'discount', 'product_id as productID', 'type as orderType', 'tax_id as taxID', 'quantity', 'variant_id as variantID', 'note as cartItemNote'], 'order_id', $rowOrderItem->orderID);
            foreach ($modified_cart as $rowItem) {
                if ($rowItem->taxID) {
                    $rowItem->productTaxPercentage = Tax::getFirst('percentage', 'id', $rowItem->taxID)->percentage;
                } else {
                    $rowItem->productTaxPercentage = 0;
                }

                $rowItem->returnType = $rowOrderItem['return_type'];


                if ($rowItem->variantID != null) {
                    $firstVariant = ProductVariant::getFirst('variant_title', 'id', $rowItem->variantID);
                    $rowItem->variantTitle = $firstVariant ? $firstVariant->variant_title : "";

                    $firstProductTitle = Product::getFirst('title', 'id', $rowItem->productID);
                    $rowItem->productTitle = $firstProductTitle ? $firstProductTitle->title : "";
                }

                $rowItem->showItemCollapse = false;
                $rowItem->calculatedPrice = $rowItem->quantity * $rowItem->price;
            }

            if ($rowOrderItem->customer != null) {
                $rowOrderItem->customer = Customer::getFirst(['first_name', 'last_name', 'email', 'id'], 'id', $rowOrderItem->customer);
                $rowOrderItem->customer->customer_group_discount = 0;
            }

            if ($rowOrderItem->supplier_id != null) {
                $rowOrderItem->customer = Supplier::getOne($rowOrderItem->supplier_id);
                $rowOrderItem->customer->customer_group_discount = 0;
            }
    
        }

        return $orderItems;
    }
    public function setSalesReturnsType(Request $request)
    {
        $salesReturnType = $request->salesOrReturnType;
        Setting::updateSetting('sales_return_status', $salesReturnType);
    }

    public function setPurchaseReturnsType(Request $request)
    {
        $purchaseReturnType = $request->purchaseOrReturnType;
        Setting::updateSetting('purchase_return_status', $purchaseReturnType);
    }

    public function getProductNew(Request $request)
    {
        
        $shortcutSettings = $this->getShortcutSettings();
        $options = $this->optionShaper($request);
// \DB::enableQueryLog();
        $products = Product::getAllProducts($options);
// dd(\DB::getQueryLog());        
        $productVariants = Product::getProductVariantsList(
            $options['branchId'],
            $options['orderType'],
            $options['onlyInStockProducts'],
            $request->rowLimit ? $products : null
        );

        return [
            'products' => $products,
            'count' => $products->count(),
            'barcodeResultValue' => null,
            'shortcutSettings' => $shortcutSettings,
            'variants' => $productVariants,
            // 'total_products' => Product::query()->count('id')
            'total_products' => Product::where('branch_id', $options['branchId'])->count('id')

        ];
    }

    public function getSelectedProductNew(Request $request)
    {
        // Check the value of currentBranch
        if ($request->currentBranch != 1) {
            return null;
        }   
        $shortcutSettings = $this->getShortcutSettings();
        $options = $this->optionShaper($request);
        // \DB::enableQueryLog();
        $products = Product::getSelectedProducts($options, $request->id);
        // dd(\DB::getQueryLog());        
        $productVariants = Product::getProductVariantsList(
            $options['branchId'],
            $options['orderType'],
            $options['onlyInStockProducts'],
            $request->rowLimit ? $products : null
        );

        $showCombo = false;

        #start
        $customerSubquery = DB::table('customers')->select('course_id', 'gender_id') ->where('id', $request->id)->first();
        $courseId = $customerSubquery->course_id;
        $genderId = $customerSubquery->gender_id;

        // Step 2: Find the package ID from product_packages
        $packageIdSubquery = DB::table('product_packages')
            ->select('id')
            ->where('course_id', $courseId)
            ->where('package_for', $genderId)
            ->first();

        $packageId = isset($packageIdSubquery->id) ? $packageIdSubquery->id  : null;
        if($packageId == null){
            $packageId = 0;
        }

        // Step 3: Get the product IDs from product_package_products using GROUP_CONCAT
        $productIds = DB::table('product_package_products')
            ->where('package_id', $packageId)
            ->select(DB::raw('GROUP_CONCAT(product_id) as product_ids'))
            ->value('product_ids');

            if(!empty($productIds)){
                $showCombo = true;
            }

        #end


        return [
            'products' => $products,
            'count' => $products->count(),
            'barcodeResultValue' => null,
            'shortcutSettings' => $shortcutSettings,
            'variants' => $productVariants,
            'total_products' => Product::query()->count('id'),
            'showCombo' => $showCombo
        ];
    }


    public function getProduct($orderType, $currentBranch)
    {

        $outOfStock = 0;
        $shortcutSettings = $this->getShortcutSettings();

        $data = Product::index([
            'products.id as productID', 'products.title', 'products.taxable',
            'products.tax_type', 'products.tax_id', 'products.imageURL as productImage',
            'products.branch_id'
        ]);

        foreach ($data as $rowData) {

            if ($rowData->taxable == 0) {
                $rowData->taxPercentage = 0;
            } else {

                if ($rowData->tax_type == 'default') {

                    $branchTax = Branch::getFirst('*', 'id', $currentBranch);
                    if ($branchTax->taxable == 0) {
                        $rowData->taxPercentage = 0;
                    } else {

                        if ($branchTax->is_default == 0) {
                            $taxID = $branchTax->tax_id;
                        } else {
                            $taxID = Tax::getFirst('id', 'is_default', 1)->id;
                        }

                        $rowData->taxPercentage = Tax::getFirst('percentage', 'id', $taxID)->percentage;
                    }
                } else {
                    $rowData->taxPercentage = Tax::getFirst('percentage', 'id', $rowData->tax_id)->percentage;
                }
            }

            $productVariant = ProductVariant::getProductVariant($rowData->productID, $orderType, $outOfStock);

            foreach ($productVariant as $rowProductVariant) {
                $rowProductVariant->attribute_values = explode(',', $rowProductVariant->attribute_values);
                $rowProductVariant->availableQuantity = OrderItems::availableQuantity($rowProductVariant->id);
            }

            $attribute_name = [];
            $attribute_id = ProductAttributeValue::attributeValues($rowData->productID);

            foreach ($attribute_id as $key => $rowAttributeId) {
                $attribute_name[$key] = ProductAttribute::getFirst('name', 'id', $rowAttributeId->attribute_id)->name;
            }

            //$rowData->variants = $productVariant;
            $rowData->attributeName = $attribute_name;
        }

        return [
            'products' => $data,
            'shortcutKeyCollection' => $shortcutSettings,
        ];
    }

    public function barCodeSearch($searchValueForBarCode, $orderType)
    {
        $barCodeSearch = ProductVariant::searchProduct($searchValueForBarCode, $orderType);

        if ($barCodeSearch) {
            $barCodeSearch->cartItemNote = '';
            $barCodeSearch->discount = 0;
            $barCodeSearch->quantity = 1;
            $barCodeSearch->showItemCollapse = false;
            $barCodeSearch->discountType = '%';
            $barCodeSearch->calculatedPrice = $barCodeSearch->price;

            if ($barCodeSearch->taxable == 0) {
                $barCodeSearch->productTaxPercentage = 0;
            } else {
                if ($barCodeSearch->tax_type == 'default') {
                    $branchTax = Branch::getFirst('is_default', 'id', $barCodeSearch->branch_id)->is_default;

                    if ($branchTax == 0) {
                        $taxID = Branch::getFirst('tax_id', 'id', $barCodeSearch->branch_id)->tax_id;
                    } else {
                        $taxID = Tax::getFirst('id', 'is_default', 1)->id;
                    }
                    $barCodeSearch->productTaxPercentage = Tax::getFirst('percentage', 'id', $taxID)->percentage;
                } else {
                    $barCodeSearch->productTaxPercentage = Tax::getFirst('percentage', 'id', $barCodeSearch->taxID)->percentage;
                }
            }
        }

        unset($barCodeSearch->tax_type);
        unset($barCodeSearch->taxable);
        return $barCodeSearch;
    }

    public function setBranch(Request $request)
    {
        $allSetting = new AllSettingFormat;
        $authID = Auth::user('id')->id;
        $branchID = $request->branchID;
        $orderType = $request->orderType;
        $currentBranch = $allSetting->getCurrentBranch();
//        $products = $this->getProduct($orderType, $branchID);
//        $products = $this->getProductNew($request);
        if ($currentBranch) {
            Setting::updateCurrentBranch($authID, $branchID);
        } else {
            Setting::store([
                'setting_name' => 'current_branch',
                'setting_value' => $branchID,
                'setting_type' => 'user',
                'user_id' => $authID,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ]);
        }

        \DB::table('users')->where('id', \Auth::user()->id)->update(['dashboard_branch_id' => $branchID]);

//        return ['products' => []];
    }

    public function updateVariantPurchasePrice($data)
    {
        $updatableData = [
            'purchase_price' => $data['price']
        ];
        ProductVariant::updateData($data['variantID'], $updatableData);
    }

    public function updateVariantSalesPrice($data)
    {
        $updatableData = [
            'selling_price' => $data['price']
        ];
        ProductVariant::updateData($data['variantID'], $updatableData);
    }

    public function salesStore(Request $request)
    {
        Log::info("\n".'----------------New Order------------------');
        Log::info('Request Data', [$request]);

        $requested_id = collect($request->cart)->pluck('productID');
        $cashRegister = $request->cashRagisterId;
        $allSettings = new AllSettingFormat;
        $userId = Auth::id();
        $userBranchId = Setting::getFirst('*', 'user_id', $userId)->setting_value;
        $date = Carbon::now()->toDateString();
        $orderType = $request->orderType;
        $salesOrReceivingType = $request->salesOrReceivingType;
        $orderStatus = $request->status;
        $createdBy = Auth::user()->id;
        $carts = $request->cart;
        $id = $request->customer ? $request->customer['id'] : null;
        $sales_person_id = $request->salesPerson ? $request->salesPerson['id'] : 0;
        $subTotal = $request->subTotal;
        $tax = $request->tax;
        $allDiscount = $request->discount; //percentage discount on entire sell
        $grandTotal = $request->grandTotal;
        $payment = isset($request->payments) ? $request->payments : [];
        $orderID = $request->orderID;
        $orderIdInternalTransfer = $request->orderIdInternalTransfer;
        $transferBranch = $request->transferBranch;
        $transferBranchName = $request->transferBranchName;
        $dueAmount = 0;
        $time = $request->time;
        $restaurantTableId = $request->tableId;
        $variantTitle = '';
        $message = [];
        $orderData1 = [];
        $allOrderData1 = [];
        $totalProductInThisInvoice = 0;
        $totalReturnedProduct = 0;
        $totalCartProduct = 0;
        $returnProductProfit = 0;
        $total_qty =0;
        $quotation_id = isset($request->quotation_id) ? $request->quotation_id : null;
        $orderCustomer = null;
        $typeOfSale = Auth::user()->sale_type;
        $image = $request->paymentReferenceFile;
        $spaceBasic = 0;

        $imageHandler = new imageHandler;
        if ($file = $image) {
            $fileNameToStore = $imageHandler->imageUpload($image, 'pay_ref_', 'uploads/pay_ref/');
        }

        // if($orderType == 'sales'  && $request->salesOrReturnType == 'returns'){
        //     $orderID = $request->orderID = null;
        // }

        Log::info('New order request type : '.$orderType.' & '. $request->salesOrReturnType);

        if($orderType == 'sales'){
            $orderID = $request->orderID = null;
        }
 
        /*Section for Return Order product in cart*/
        if ($request->salesOrReturnType == 'returns') {
            $orderItemProductCount = 0;
            //checking cart with discount
            foreach ($carts as $cart) if ($cart['orderType'] == 'sales') $orderItemProductCount++;
            $invoiceReturnId = isset($request->cart[0]['invoiceReturnId']) ? $request->cart[0]['invoiceReturnId'] : '';

            $salingProfit = Order::getFirst(['id', 'profit'], 'invoice_id',$invoiceReturnId );
            $salingProfitId = isset($salingProfit->id) ? $salingProfit->id : null;

            $returnInvoiceProfit = Order::getReturnProductProfit($invoiceReturnId);

            if ($returnInvoiceProfit->profit == null) $returnInvoiceProfit->profit = 0;
            
            $productsSoldFirstCount = 0;
            if($salingProfitId)
                $productsSoldFirstCount = OrderItems::countRecord('order_id', $salingProfitId);
        }
        /*Section for Return Order product in cart ends*/

        $totalCartProduct = (collect($carts)->sum('quantity')) * (-1);

 
        // if ($grandTotal < 0)
         {

            $invoiceReturnId = isset($request->cart[0]['invoiceReturnId']) ? $request->cart[0]['invoiceReturnId'] : '';

            $orderDetailsInformation = Order::getOrderInformation($invoiceReturnId, $orderType);

            if (count($orderDetailsInformation) > 0) $returnProductProfit = $orderDetailsInformation[0]->profit; //checking profit while return product

            $totalProductInThisInvoice = (collect($orderDetailsInformation)->sum('quantity')) * (-1);

            $diffQuantity = $orderDetailsInformation
                ->whereIn('product_id', $requested_id)
                ->filter(function ($order_item) use ($request) {
                    $item = collect($request->cart)->first(function ($item) use ($order_item) {
                        return $item["productID"] == $order_item->product_id;
                    });
                    $order_item->quantity_difference = $item["quantity"] - $order_item->quantity;
                    return $item["quantity"] > $order_item->quantity;
                });

            $orderData1 = $orderDetailsInformation->whereNotIn('product_id', $requested_id);
            $allOrderData1 = collect($orderData1)->merge($diffQuantity)->map(function ($order) {
                $item["product_id"] = $order->product_id;
                $item["variant_id"] = $order->variant_id;
                if ($item["quantity"] = $order->quantity_difference == null) {
                    $item["quantity"] = $order->quantity * -1;
                } else {
                    $item["quantity"] = $order->quantity_difference;
                }
                return $item;
            });
        } 


        $outOfStock = Setting::getSettingValue('out_of_stock_products')->setting_value;
        $checkAvailableQuantity = 'false';

        foreach ($request->cart as $cart) {
            $availableQuantityCheck = OrderItems::checkAvailableQuantity($cart['variantID']);

            if ($cart['orderType'] !== 'discount') {
                $outOfStockVariantTitle = $cart['variantTitle'] ? ' (' . $cart['variantTitle'] . ') ' : ' ' . $cart['variantTitle'] . ' ';
                if ($outOfStock == 1 && $request->orderType == 'sales' && $request->status == 'done' && $cart['quantity'] > $availableQuantityCheck && $cart['quantity'] > 0) {
                    $checkAvailableQuantity = 'true';
                    if ($availableQuantityCheck <= 0) {
                        array_push($message, $cart['productTitle'] . $outOfStockVariantTitle . trans('lang.is_out_of_stock') . ' ' . trans('lang.please_remove_from_cart'));
                    } else {
                        array_push($message, $cart['productTitle'] . $outOfStockVariantTitle . trans('lang.is_out_of_stock') . ' ' . trans('lang.available_quantity') . '' . $availableQuantityCheck . '.');
                    }
                }
            }


            if ($orderType == 'receiving'
                || $salesOrReceivingType == 'internal'
                || $salesOrReceivingType == 'internal-transfer') {
                // $this->updateVariantPurchasePrice($cart);
            } else {
                // $this->updateVariantSalesPrice($cart);
            }
        }

        if ($checkAvailableQuantity == 'true') {

            $response = [
                'checkAvailableQuantity' => $checkAvailableQuantity,
                'message' => $message
            ];

            Log::error('checkAvailableQuantity : ',$response);

            return response()->json($response, 200);
        }
        $lastInvoiceNumber          = Setting::getSettingValue('last_invoice_number')->setting_value;
        $purchaseLastInvoiceNumber  = '';//Setting::getSettingValue('purchase_last_invoice_number')->setting_value;

        Log::info('lastInvoiceNumber : '.$lastInvoiceNumber);

        $profit = $request->profit == null ? 0 : $request->profit;

        $invoiceFixes = $allSettings->getInvoiceFixes();

        if ($allSettings->getCurrentBranch()->is_cash_register == 1) {
            $cashRegisterID = $this->getCashRegisterID()->id;
        } else {
            $cashRegisterID = null;
        }

        if ($allDiscount == null) {
            $allDiscount = 0;
        }

        if (!empty($payment)) {
            $paymentMethod = env('PAYMENT_METHOD_SPACE_BASIC', 6); // fallback to 6
            foreach ($payment as $key => $value) {
                if ($value['paymentType'] == 'credit' || strtolower($value['paymentName']) == 'credit') {
                    $dueAmount = floatval($value['paid']);
                }
                if ($value['paymentID'] == $paymentMethod && count($payment) == 1) {
                    $latestPayment = CustomerPayment::where('customer_id', $id)->latest()->first();
                    $spaceBasic = isset($latestPayment) ? $latestPayment->paid : 0;
                }
            }
        }

        Log::info('orderID : '.$orderID);

        if (($orderStatus == 'done' && !$orderID) || ($orderStatus == 'pending' && !$orderID) || ($orderStatus == 'hold' && !$orderID)) {
            $orderData = array();
            $orderData['date'] = $date;
            $orderData['sales_note'] = $request->salesNote;
            $orderData['all_discount'] = $allDiscount;
            $orderData['sub_total'] = $subTotal;
            $orderData['total_tax'] = $tax;
            $orderData['due_amount'] = $dueAmount;
            $orderData['total'] = $grandTotal;
            $orderData['type'] = $salesOrReceivingType;
            $orderData['profit'] = $profit;
            $orderData['status'] = $orderStatus;
            $orderData['table_id'] = $restaurantTableId;
            $orderData['sales_user_id'] = $sales_person_id; 

            // if ($orderData['total'] >= 0 || $salesOrReceivingType == "internal-transfer") {
            if ($orderData['total'] >= 0 && $request->salesOrReturnType == 'sales' ) {
                $orderData['order_type'] = $orderType;
            } else {
                //return product section
                $invoiceReturnId = isset($carts[0]['invoiceReturnId']) ? $carts[0]['invoiceReturnId'] : null;
                $getReturnProduct = Order::getReturnProduct($invoiceReturnId, $orderType);

                $totalReturnedProduct = collect($getReturnProduct)->sum('quantity');

                // var_dump(count($allOrderData1));


                // if (count($allOrderData1) > 0) {
                    if ($orderType == 'receiving') {
                        $totalProductInThisInvoice = $totalProductInThisInvoice * -1;
                    }
                    if ($totalProductInThisInvoice - $totalReturnedProduct == $totalCartProduct) {
                        $returnType = 'fully';
                    } else {
                        $returnType = 'partial';
                    }
                // } else {
                //     $returnType = 'fully';
                // }
                // var_dump($returnType);
                // die;
   

                $orderData['order_type'] = $orderType;

                $orderData['type'] = $salesOrReceivingType;
                $orderData['returned_invoice'] = isset($carts[0]['invoiceReturnId']) ? $carts[0]['invoiceReturnId'] : null;

                /*profit calculation for return product*/
                if ($request->salesOrReturnType == 'returns') {
                    if ($returnType == 'fully') {
                        $orderData['profit'] = ($returnProductProfit - $returnInvoiceProfit->profit) * (-1);
                    } else {
                        $salingProfitProfit = isset($salingProfit->profit) ? $salingProfit->profit : 0;
                        $returnInvoiceProfitprofit = isset($returnInvoiceProfit->profit) ? $returnInvoiceProfit->profit : 0;
                        $availableProfit = $salingProfitProfit - $returnInvoiceProfitprofit;
                        $orderData['profit'] = 0;
                        if($productsSoldFirstCount)
                            $orderData['profit'] = $availableProfit / $productsSoldFirstCount * (-1);

                    }
                } else {
                    //for purchase return
                    $orderData['profit'] = 0;
                }
            }

            if ($salesOrReceivingType == 'internal' || $salesOrReceivingType == 'internal-transfer') {
                $orderData['transfer_branch_id'] = $transferBranch;
            }

            $orderType === 'sales' ? $orderData['customer_id'] = $id : $orderData['supplier_id'] = $id;

            $orderData['created_by'] = $createdBy;
            $orderData['branch_id'] = $userBranchId;
            $orderData['created_at'] = Carbon::parse($time);
            $orderData['payment_reference_file']  = isset($fileNameToStore) ? $fileNameToStore : null;
            $orderData['sale_type'] =$typeOfSale;
 

            if ($orderData['table_id']) {
                RestaurantTable::updateTableStatus($orderData['table_id'], 'booked');
            }

            $orderLastId = Order::store($orderData);


            if ($salesOrReceivingType == 'internal-transfer') {
                $orderIdInternalTransfer = $this->insertInternalTransfer($orderData, $transferBranch, $userBranchId, $invoiceFixes, $purchaseLastInvoiceNumber);
            }
            // echo "<pre>"; print_r($orderLastId->total ); echo "</pre>";
            // echo "<pre>"; print_r($carts ); echo "</pre>";
            // echo "<pre>"; print_r($returnType ); echo "</pre>";
            // echo "<pre>"; print_r($orderType ); echo "</pre>";

            if ($orderLastId->total < 0 && $orderLastId->status == 'done') {
                Order::updateOrderType($carts[0]['invoiceReturnId'], $returnType, $orderType);
            }

            Log::info('OrderInfo After Saving in orders table  : ',[$orderLastId]);


            if ($request->shippingAreaId != null && $orderStatus == 'done') {
                $setShippingInfo = $this->storeShippingInformation($request, $orderLastId->id);
            }

            $orderID = $orderLastId->id;

            if($quotation_id != null){
                PurchaseQuotation::whereIn('id', $quotation_id)->update(['order_id' => $orderID]);
            }

            if ($orderLastId->order_type == 'sales') {
                
                #check and update the latest invoice id from db 
                $updateInvoiceNumberGen = $invoiceFixes['prefix'] . $lastInvoiceNumber . $invoiceFixes['suffix'];
                $exists = Order::where('invoice_id', $updateInvoiceNumberGen)->exists();
                if($exists){
                    do { 
                        $lastInvoiceNumber = $lastInvoiceNumber + 1; 
                        $updateInvoiceNumberGen = $invoiceFixes['prefix'] . $lastInvoiceNumber . $invoiceFixes['suffix']; 
                        $exists = Order::where('invoice_id', $updateInvoiceNumberGen)->exists();
                        
                    } while ($exists); 
                }

                Order::updateData($orderID, ['invoice_id' => $updateInvoiceNumberGen]);
                $lastInvoiceNumber += 1;

                $lastUpdatedInvoice = Setting::where('setting_name', 'last_invoice_number')->first()->setting_value;
                if ($lastInvoiceNumber > $lastUpdatedInvoice) {
                    Setting::updateSetting('last_invoice_number', $lastInvoiceNumber);
                }
            } else {
                Order::updateData($orderID, ['invoice_id' => $invoiceFixes['purchasePrefix'] . $purchaseLastInvoiceNumber . $invoiceFixes['purchaseSuffix']]);
                $purchaseLastInvoiceNumber += 1;

                $purchaseLastUpdatedInvoice = Setting::where('setting_name', 'purchase_last_invoice_number')->first()->setting_value;
                if ($purchaseLastInvoiceNumber > $purchaseLastUpdatedInvoice) {
                    Setting::updateSetting('purchase_last_invoice_number', $purchaseLastInvoiceNumber);
                }
            }
            
        } else {
            $orders = array();
            $orders['date'] = $date;
            $orders['sales_note'] = $request->salesNote;
            $orders['order_type'] = $orderType;
            $orders['all_discount'] = $allDiscount;
            $orders['sub_total'] = $subTotal;
            $orders['total_tax'] = $tax;
            $orders['total'] = $grandTotal;
            $orders['type'] = $salesOrReceivingType;
            $orders['status'] = $orderStatus;
            $orders['table_id'] = $restaurantTableId;
            $orders['due_amount'] = $dueAmount;
            $orderData['sales_user_id'] = $sales_person_id; 

            if ($orders['total'] < 0) {
                $getReturnProduct = Order::getReturnProduct($carts[0]['invoiceReturnId'], $orderType);
                $totalReturnedProduct = collect($getReturnProduct)->sum('quantity');

                if ($orderType == 'receiving') {
                    $totalProductInThisInvoice = $totalProductInThisInvoice * -1;
                }

                Order::updateOrderType(
                    $carts[0]['invoiceReturnId'],
                    $totalProductInThisInvoice - $totalReturnedProduct == $totalCartProduct ? 'fully' : 'partial',
                    $orderType
                );

                $orders['order_type'] = $orderType;
            }

            if ($salesOrReceivingType == 'internal') {
                $orders['transfer_branch_id'] = $transferBranch;
            }


            $orderType == 'sales' ? $orders['customer_id'] = $id : $orders['supplier_id'] = $id;

            $orders['created_by'] = $createdBy;

            if ($orders['table_id']) {
                RestaurantTable::updateTableStatus($orders['table_id'], 'available');
            }

            Order::updateData($request->orderID, $orders);

            Log::info('Return Orders  : ',["orderId".$request->orderID , $orders ]);

            if ($request->shippingAreaId != null && $orderStatus == 'done') {
                $setShippingInfo = $this->storeShippingInformation($request, $request->orderID);
            }
            if ($salesOrReceivingType == 'internal-transfer') {
                $orders['order_type'] = 'receiving';
                Order::updateData($request->orderIdInternalTransfer, $orders);
            }
        }
        $orderItems = [];
        $orderItemsInternalTransfer = [];

        foreach ($carts as $cart) {
            $orderType == 'sales' ? $quantity = -$cart['quantity'] : $quantity = $cart['quantity'];

            if (!array_key_exists('discount', $cart) || $cart['discount'] == null) {
                $cart['discount'] = 0;
            }

            $ordered_quantity =0;
            $grn_status = "done";
            if( $orderType == 'receiving'){
                $ordered_quantity = $quantity;
                $quantity = 0;
                $grn_status = "receiving";
            }

            array_push($orderItems, [
                'product_id' => $cart['productID'],
                'variant_id' => $cart['variantID'],
                'type' => $cart['orderType'],
                'quantity' => $quantity,
                'ordered_quantity' => $ordered_quantity,
                'price' => $cart['price'],
                'discount' => $cart['discount'],
                'sub_total' => $cart["calculatedPrice"],
                'tax_id' => $cart['taxID'],
                'order_id' => $orderID,
                'note' => $cart['cartItemNote'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'grn_status' => $grn_status,
            ]);

            // for update isNotify product_variant
            if (isset($cart['variantID'])) {
                ProductVariant::removeBranchFromIsNotify($cart['variantID'], $request->branchId);
            }

            if ($salesOrReceivingType == 'internal-transfer') {
                $quantity = $cart['quantity'];
                array_push($orderItemsInternalTransfer, [
                    'product_id' => $cart['productID'],
                    'variant_id' => $cart['variantID'],
                    'type' => 'receiving',
                    'quantity' => $quantity,
                    'price' => $cart['price'],
                    'discount' => $cart['discount'],
                    'sub_total' => $cart["calculatedPrice"],
                    'tax_id' => $cart['taxID'],
                    'order_id' => $orderIdInternalTransfer,
                    'note' => $cart['cartItemNote'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
        }

        if ($orderStatus != 'hold') {
            if (sizeof($payment) > 0) {
                $paymentArray = [];
                $paymentArrayInternal = [];

                foreach ($payment as $rowPayment) {
                    array_push($paymentArray, ['date' => $date, 'paid' => $rowPayment['paid'], 'exchange' => $rowPayment['exchange'], 'payment_method' => $rowPayment['paymentID'], 'options' => serialize($rowPayment['options']), 'order_id' => $orderID, 'cash_register_id' => $cashRegisterID, 'is_active' => $rowPayment['is_active'], 'created_at' => $rowPayment['PaymentTime']]);
                }
                foreach ($payment as $rowPayment) {
                    array_push($paymentArrayInternal, ['date' => $date, 'paid' => $rowPayment['paid'], 'exchange' => $rowPayment['exchange'], 'payment_method' => $rowPayment['paymentID'], 'options' => serialize($rowPayment['options']), 'order_id' => $orderIdInternalTransfer, 'cash_register_id' => $cashRegisterID, 'is_active' => $rowPayment['is_active'], 'created_at' => $rowPayment['PaymentTime']]);
                }

                if (($orderStatus == 'done' && !$orderID) || ($orderStatus == 'pending' && !$orderID)) {
                    Payments::insertData($paymentArray);
                } else {
                    Payments::deleteRecord('order_id', $request->orderID);
                    Payments::insertData($paymentArray);
                    if ($salesOrReceivingType == 'internal-transfer') {
                        Payments::deleteRecord('order_id', $orderIdInternalTransfer);
                        Payments::insertData($paymentArrayInternal);
                    }
                }
            }
        }

        if ($orderType == 'sales') {
            $invoiceId = $invoiceFixes['prefix'] . $invoiceFixes['lastInvoiceNumber'] . $invoiceFixes['suffix'];
        } else $invoiceId = $invoiceFixes['purchasePrefix'] . $invoiceFixes['purchaseLastInvoiceNumber'] . $invoiceFixes['purchaseSuffix'];


        if (($orderStatus == 'done' && $orderID == null)) {
            OrderItems::insertData($orderItems);
            $response = [
                'invoiceID' => $invoiceId,
            ];
            return $response;
        } else if (($orderStatus == 'pending' && $orderID == null)) {
            OrderItems::insertData($orderItems);
            $response = [
                'orderID' => $orderID,
                'orderIdInternalTransfer' => $orderIdInternalTransfer
            ];
            return $response;
        } else {
            OrderItems::deleteRecord('order_id', $request->orderID);
            OrderItems::insertData($orderItems);
            if ($salesOrReceivingType == 'internal-transfer') {
                OrderItems::insertData($orderItemsInternalTransfer);
            }
            if ($orderType == 'sales') {
                $invoiceId = $invoiceFixes['prefix'] . $invoiceFixes['lastInvoiceNumber'] . $invoiceFixes['suffix'];
                $lastInvoiceId = Setting::getSettingValue('last_invoice_number')->setting_value;
            } else {
                $invoiceId = $invoiceFixes['purchasePrefix'] . $invoiceFixes['purchaseLastInvoiceNumber'] . $invoiceFixes['purchaseSuffix'];
                $lastInvoiceId = Setting::getSettingValue('purchase_last_invoice_number')->setting_value;
            }

            if ($orderStatus == 'done') {
                // send customer invoice
                try {

                    $invoiceTemplateEmail = new InvoiceTemplateController();
                    $invoiceTemplateData = $invoiceTemplateEmail->getInvoiceTemplateToPrint($orderID, $salesOrReceivingType, $transferBranchName, $cashRegister, $orderType, 'email');

                    $autoEmailReceive = Setting::getSettingValue('auto_email_receive')->setting_value;
                    
                    // \DB::enableQueryLog();
                    $orderDetails = Order::orderDetails($orderID, $cashRegister);
                    // dd(\DB::getQueryLog());
                    // Sms receive to customer
                    $autoSmsReceive = Setting::getSettingValue('sms_recive_to_customer')->setting_value;

                

                    $orderCustomerState  = null;
                    if (isset($orderDetails->customer_id) &&  $orderDetails->customer_id != null) {
                        $orderCustomer = Customer::getOne($orderDetails->customer_id);

                        $orderCustomerState = $orderCustomer->state;

                        if ($autoSmsReceive == 1 && $orderCustomer->phone_number) {
                            $this->autoCustomerSmsSend($orderCustomer->first_name, $orderCustomer->last_name, $orderCustomer->phone_number, $orderDetails->invoice_id, $orderDetails->total);

                        }

                        if ($autoEmailReceive == 1 && $orderCustomer->email) {

                            $content = EmailTemplate::query()->select('template_subject', 'default_content', 'custom_content')->where('template_type', 'pos_invoice')->first();

                            $subject = $content->template_subject;
                            $text = $content->custom_content ?  $content->custom_content : $content->default_content;

                            $mailText = str_replace('{first_name}', $orderCustomer->first_name, str_replace('{invoice_id}', $orderDetails->invoice_id, str_replace('{app_name}', Config::get('app_name'), $text)));

                            $this->sendPdf($invoiceTemplateData['data'], $orderID, $cashRegister, $mailText, $orderCustomer->email, $subject);

                        }
                    }
                    if(isset($orderDetails->supplier_id) && $orderDetails->supplier_id != null && $orderType == 'receiving'){
                        $orderCustomer = Supplier::getOne($orderDetails->supplier_id);

                        $orderCustomerState = '25';//Telangana

                        if ($autoSmsReceive == 1 && $orderCustomer->phone_number) {
                            $this->autoCustomerSmsSend($orderCustomer->first_name, $orderCustomer->last_name, $orderCustomer->phone_number, $orderDetails->invoice_id, $orderDetails->total);

                        }

                        if ($autoEmailReceive == 1 && $orderCustomer->email) {

                            $content = EmailTemplate::query()->select('template_subject', 'default_content', 'custom_content')->where('template_type', 'pos_invoice')->first();

                            $subject = $content->template_subject;
                            $text = $content->custom_content ?  $content->custom_content : $content->default_content;

                            $mailText = str_replace('{first_name}', $orderCustomer->first_name, str_replace('{invoice_id}', $orderDetails->invoice_id, str_replace('{app_name}', Config::get('app_name'), $text)));

                            $this->sendPdf($invoiceTemplateData['data'], $orderID, $cashRegister, $mailText, $orderCustomer->email, $subject);

                        }
                    }

                } catch (\Exception $e) {
                    return $e;
                }

                $invoiceTemplate = new InvoiceTemplateController();

                $templateData = $invoiceTemplate->getInvoiceTemplateToPrint($orderID, $salesOrReceivingType, $transferBranchName, $cashRegister, $orderType, 'receipt');
                if($orderType == "receiving"){
                    $productDetails = $this->getOrderItemssummary($orderID);
                    $productData = "";
                    $slNO = 1;
                    foreach ($productDetails['data'] as $item) {
                        $productData .= "<tr>";
                        $productData .= "<tr><td>".$slNO++."</td>";
                        $productData .= "<td>".$item->title."</td>";
                        $productData .= "<td>".$item->short_name."</td>";
                        $productData .= "<td>".$item->orderquantity."</td>";
                        $productData .= "<td>".$item->price."</td>";
                        $productData .= "<td>".$item->total_price."</td>";
                        $productData .= "</tr>";
                    }

                    


                    $templateData = str_replace('{product_details}', $productData, $templateData);
                    $totalAmountInWords = currencyToWords($grandTotal, 'INR');
                    $templateData = str_replace('{total_sum}',  $allSettings->getCurrency(number_format($grandTotal, 2)), $templateData);
                    $templateData = str_replace('{total_qty}', $total_qty, $templateData);
                    $templateData = str_replace('{tax}', $tax, $templateData);
                    $templateData = str_replace('{sub_total}', number_format($subTotal, 2), $templateData);
                    $templateData = str_replace('{customer_name}', $orderCustomer ? $orderCustomer->first_name.$orderCustomer->last_name : '', $templateData);
                    $templateData = str_replace('{customer_phone}', $orderCustomer ? $orderCustomer->phone_number : '', $templateData);
                    $taxPercentage = ($subTotal != 0) ? ($tax / $subTotal) * 100 : 0;
                    $templateData = str_replace('{gst_percentage}', number_format($taxPercentage, 2), $templateData);
                    $templateData = str_replace('{totalAmountInWords}', $totalAmountInWords, $templateData);
                }
                if ($request->branchId == 1) {
                    $templateData = preg_replace('/<tr>\s*<td>\s*Total\s*<\/td>\s*<td>.*?<\/td>\s*<\/tr>/i', '', $templateData);
                }
                if($typeOfSale == 'combo' && $spaceBasic > 0) {
                    $allSettings = new AllSettingFormat;
                    $spaceBasic = $allSettings->getCurrency($allSettings->thousandSep($spaceBasic));
                    $templateData = preg_replace_callback(
                        '/(<tr>\s*<td>\s*Paid in Spacebasic\s*<\/td>\s*<td>)₹[\d,.]+(<\/td>\s*<\/tr>)/i',
                        function ($matches) use ($spaceBasic) {
                            return $matches[1] . $spaceBasic . $matches[2];
                        },
                        $templateData
                    );
                }
                $clientState = Config::get('client_location_state');
                $gstType = $clientState == $orderCustomerState ? 'SGST' : 'IGST';
                $gstType = is_null($orderCustomerState) ? 'SGST' : 'IGST';
                
                Log::info('Order Response: ', [
                    'orderID' => $orderID,
                    'orderIdInternalTransfer' => $orderIdInternalTransfer,
                    'invoiceID' => $invoiceId,
                    'message' => Lang::get('lang.payment_done_successfully'),
                    'lastInvoiceId' => $lastInvoiceId,
                    'time' => date('d/m/Y h:i a')
                ]);
                 

                $response = [
                    'orderID' => $orderID,
                    'orderIdInternalTransfer' => $orderIdInternalTransfer,
                    'invoiceID' => $invoiceId,
                    'message' => Lang::get('lang.payment_done_successfully'),
                    'invoiceTemplate' => $templateData,
                    'orderCustomerState' => $orderCustomerState,
                    'clientState' => $clientState,
                    'gstType' => $gstType,
                    'lastInvoiceId' => $lastInvoiceId,
                    'time' => date('d/m/Y h:i a')
                ];

                return $response;
            } else {
                $response = [
                    'orderID' => $orderID,
                    'orderIdInternalTransfer' => $orderIdInternalTransfer,
                    'invoiceID' => $invoiceId,
                    'message' => Lang::get('lang.payment_done_successfully'),
                    'lastInvoiceId' => $lastInvoiceId,
                ];


                Log::info('Order Response: ', $response);

                return $response;
            }
        }
    }

    public function purchaseStore(Request $request)
    {
        $requested_id = collect($request->cart)->pluck('productID');
        $cashRegister = $request->cashRagisterId;
        $allSettings = new AllSettingFormat;
        $userId = Auth::id();
        $userBranchId = Setting::getFirst('*', 'user_id', $userId)->setting_value;
        $date = Carbon::now()->toDateString();
        $orderType = $request->orderType;
        $salesOrReceivingType = $request->salesOrReceivingType;
        $orderStatus = $request->status;
        $createdBy = Auth::user()->id;
        $carts = $request->cart;
        $id = $request->customer ? $request->customer['id'] : null;
        $sales_person_id = $request->salesPerson ? $request->salesPerson['id'] : 0;
        $subTotal = $request->subTotal;
        $tax = $request->tax;
        $allDiscount = $request->discount; //percentage discount on entire sell
        $grandTotal = $request->grandTotal;
        $payment = isset($request->payments) ? $request->payments : [];
        $orderID = $request->orderID;
        $orderIdInternalTransfer = $request->orderIdInternalTransfer;
        $transferBranch = $request->transferBranch;
        $transferBranchName = $request->transferBranchName;
        $dueAmount = 0;
        $time = $request->time;
        $restaurantTableId = $request->tableId;
        $variantTitle = '';
        $message = [];
        $orderData1 = [];
        $allOrderData1 = [];
        $totalProductInThisInvoice = 0;
        $totalReturnedProduct = 0;
        $totalCartProduct = 0;
        $returnProductProfit = 0;
        $total_qty =0;
        $quotation_id = isset($request->quotation_id) ? $request->quotation_id : null;
        $orderCustomer = null; 
 
        /*Section for Return Order product in cart*/
        if ($request->salesOrReturnType == 'returns') {
            $orderItemProductCount = 0;
            //checking cart with discount
            foreach ($carts as $cart) if ($cart['orderType'] == 'sales') $orderItemProductCount++;
            $invoiceReturnId = isset($request->cart[0]['invoiceReturnId']) ? $request->cart[0]['invoiceReturnId'] : '';

            $salingProfit = Order::getFirst(['id', 'profit'], 'invoice_id',$invoiceReturnId );

            $returnInvoiceProfit = Order::getReturnProductProfit($invoiceReturnId);

            if ($returnInvoiceProfit->profit == null) $returnInvoiceProfit->profit = 0;

            $productsSoldFirstCount = OrderItems::countRecord('order_id', $salingProfit->id);
        }
        /*Section for Return Order product in cart ends*/

        $totalCartProduct = (collect($carts)->sum('quantity')) * (-1);


        if ($grandTotal < 0) {

            $invoiceReturnId = isset($request->cart[0]['invoiceReturnId']) ? $request->cart[0]['invoiceReturnId'] : '';

            $orderDetailsInformation = Order::getOrderInformation($invoiceReturnId, $orderType);

            if (count($orderDetailsInformation) > 0) $returnProductProfit = $orderDetailsInformation[0]->profit; //checking profit while return product

            $totalProductInThisInvoice = (collect($orderDetailsInformation)->sum('quantity')) * (-1);

            $diffQuantity = $orderDetailsInformation
                ->whereIn('product_id', $requested_id)
                ->filter(function ($order_item) use ($request) {
                    $item = collect($request->cart)->first(function ($item) use ($order_item) {
                        return $item["productID"] == $order_item->product_id;
                    });
                    $order_item->quantity_difference = $item["quantity"] - $order_item->quantity;
                    return $item["quantity"] > $order_item->quantity;
                });

            $orderData1 = $orderDetailsInformation->whereNotIn('product_id', $requested_id);
            $allOrderData1 = collect($orderData1)->merge($diffQuantity)->map(function ($order) {
                $item["product_id"] = $order->product_id;
                $item["variant_id"] = $order->variant_id;
                if ($item["quantity"] = $order->quantity_difference == null) {
                    $item["quantity"] = $order->quantity * -1;
                } else {
                    $item["quantity"] = $order->quantity_difference;
                }
                return $item;
            });
        }


        $outOfStock = Setting::getSettingValue('out_of_stock_products')->setting_value;
        $checkAvailableQuantity = 'false';

        foreach ($request->cart as $cart) {
            $availableQuantityCheck = OrderItems::checkAvailableQuantity($cart['variantID']);

            if ($cart['orderType'] !== 'discount') {
                $outOfStockVariantTitle = $cart['variantTitle'] ? ' (' . $cart['variantTitle'] . ') ' : ' ' . $cart['variantTitle'] . ' ';
                if ($outOfStock == 1 && $request->orderType == 'sales' && $request->status == 'done' && $cart['quantity'] > $availableQuantityCheck && $cart['quantity'] > 0) {
                    $checkAvailableQuantity = 'true';
                    if ($availableQuantityCheck <= 0) {
                        array_push($message, $cart['productTitle'] . $outOfStockVariantTitle . trans('lang.is_out_of_stock') . ' ' . trans('lang.please_remove_from_cart'));
                    } else {
                        array_push($message, $cart['productTitle'] . $outOfStockVariantTitle . trans('lang.is_out_of_stock') . ' ' . trans('lang.available_quantity') . '' . $availableQuantityCheck . '.');
                    }
                }
            }


            if ($orderType == 'receiving'
                || $salesOrReceivingType == 'internal'
                || $salesOrReceivingType == 'internal-transfer') {
                $this->updateVariantPurchasePrice($cart);
            } else {
                $this->updateVariantSalesPrice($cart);
            }
        }

        if ($checkAvailableQuantity == 'true') {

            $response = [
                'checkAvailableQuantity' => $checkAvailableQuantity,
                'message' => $message
            ];
            return response()->json($response, 200);
        }
        $lastInvoiceNumber = Setting::getSettingValue('last_invoice_number')->setting_value;
        $purchaseLastInvoiceNumber = Setting::getSettingValue('purchase_last_invoice_number')->setting_value;

        $profit = $request->profit == null ? 0 : $request->profit;

        $invoiceFixes = $allSettings->getInvoiceFixes();

        if ($allSettings->getCurrentBranch()->is_cash_register == 1) {
            $cashRegisterID = $this->getCashRegisterID()->id;
        } else {
            $cashRegisterID = null;
        }

        if ($allDiscount == null) {
            $allDiscount = 0;
        }

        if (!empty($payment)) {
            foreach ($payment as $key => $value) {
                if ($value['paymentType'] == 'credit') {
                    $dueAmount = floatval($value['paid']);
                }
            }
        }
        if (($orderStatus == 'done' && !$orderID) || ($orderStatus == 'pending' && !$orderID) || ($orderStatus == 'hold' && !$orderID)) {
            $orderData = array();
            $orderData['date'] = $date;
            $orderData['sales_note'] = $request->salesNote;
            $orderData['all_discount'] = $allDiscount;
            $orderData['sub_total'] = $subTotal;
            $orderData['total_tax'] = $tax;
            $orderData['due_amount'] = $dueAmount;
            $orderData['total'] = $grandTotal;
            $orderData['type'] = $salesOrReceivingType;
            $orderData['profit'] = $profit;
            $orderData['status'] = $orderStatus;
            $orderData['table_id'] = $restaurantTableId;
            $orderData['sales_user_id'] = $sales_person_id; 

            if ($orderData['total'] >= 0 || $salesOrReceivingType == "internal-transfer") {
                $orderData['order_type'] = $orderType;
            } else {
                //return product section

                $getReturnProduct = Order::getReturnProduct($carts[0]['invoiceReturnId'], $orderType);

                $totalReturnedProduct = collect($getReturnProduct)->sum('quantity');

                if (count($allOrderData1) > 0) {
                    if ($orderType == 'receiving') {
                        $totalProductInThisInvoice = $totalProductInThisInvoice * -1;
                    }
                    if ($totalProductInThisInvoice - $totalReturnedProduct == $totalCartProduct) {
                        $returnType = 'fully';
                    } else {
                        $returnType = 'partial';
                    }
                } else {
                    $returnType = 'fully';
                }

                $orderData['order_type'] = $orderType;

                $orderData['type'] = $salesOrReceivingType;
                $orderData['returned_invoice'] = $carts[0]['invoiceReturnId'];

                /*profit calculation for return product*/
                if ($request->salesOrReturnType == 'returns') {
                    if ($returnType == 'fully') {
                        $orderData['profit'] = ($returnProductProfit - $returnInvoiceProfit->profit) * (-1);
                    } else {
                        $availableProfit = $salingProfit->profit - $returnInvoiceProfit->profit;
                        $orderData['profit'] = $availableProfit / $productsSoldFirstCount * (-1);
                    }
                } else {
                    //for purchase return
                    $orderData['profit'] = 0;
                }
            }

            if ($salesOrReceivingType == 'internal' || $salesOrReceivingType == 'internal-transfer') {
                $orderData['transfer_branch_id'] = $transferBranch;
            }

            $orderType === 'sales' ? $orderData['customer_id'] = $id : $orderData['supplier_id'] = $id;

            $orderData['created_by'] = $createdBy;
            $orderData['branch_id'] = $userBranchId;
            $orderData['created_at'] = Carbon::parse($time);

            if ($orderData['table_id']) {
                RestaurantTable::updateTableStatus($orderData['table_id'], 'booked');
            }


            $orderLastId = Order::store($orderData);


            if ($salesOrReceivingType == 'internal-transfer') {
                $orderIdInternalTransfer = $this->insertInternalTransfer($orderData, $transferBranch, $userBranchId, $invoiceFixes, $purchaseLastInvoiceNumber);
            }

            if ($orderLastId->total < 0 && $orderLastId->status == 'done') {
                Order::updateOrderType($carts[0]['invoiceReturnId'], $returnType, $orderType);
            }


            if ($request->shippingAreaId != null && $orderStatus == 'done') {
                $setShippingInfo = $this->storeShippingInformation($request, $orderLastId->id);
            }

            $orderID = $orderLastId->id;

            if($quotation_id != null){
                PurchaseQuotation::whereIn('id', $quotation_id)->update(['order_id' => $orderID]);
            }

            if ($orderLastId->order_type == 'sales') {                
                Order::updateData($orderID, ['invoice_id' => $invoiceFixes['prefix'] . $lastInvoiceNumber . $invoiceFixes['suffix']]);
                $lastInvoiceNumber += 1;

                $lastUpdatedInvoice = Setting::where('setting_name', 'last_invoice_number')->first()->setting_value;
                if ($lastInvoiceNumber > $lastUpdatedInvoice) {
                    Setting::updateSetting('last_invoice_number', $lastInvoiceNumber);
                }
            } else {
                Order::updateData($orderID, ['invoice_id' => $invoiceFixes['purchasePrefix'] . $purchaseLastInvoiceNumber . $invoiceFixes['purchaseSuffix']]);
                $purchaseLastInvoiceNumber += 1;

                $purchaseLastUpdatedInvoice = Setting::where('setting_name', 'purchase_last_invoice_number')->first()->setting_value;
                if ($purchaseLastInvoiceNumber > $purchaseLastUpdatedInvoice) {
                    Setting::updateSetting('purchase_last_invoice_number', $purchaseLastInvoiceNumber);
                }
            }
            
        } else {
            $orders = array();
            $orders['date'] = $date;
            $orders['sales_note'] = $request->salesNote;
            $orders['order_type'] = $orderType;
            $orders['all_discount'] = $allDiscount;
            $orders['sub_total'] = $subTotal;
            $orders['total_tax'] = $tax;
            $orders['total'] = $grandTotal;
            $orders['type'] = $salesOrReceivingType;
            $orders['status'] = $orderStatus;
            $orders['table_id'] = $restaurantTableId;
            $orders['due_amount'] = $dueAmount;
            $orderData['sales_user_id'] = $sales_person_id; 

            if ($orders['total'] < 0) {
                $getReturnProduct = Order::getReturnProduct($carts[0]['invoiceReturnId'], $orderType);
                $totalReturnedProduct = collect($getReturnProduct)->sum('quantity');

                if ($orderType == 'receiving') {
                    $totalProductInThisInvoice = $totalProductInThisInvoice * -1;
                }

                Order::updateOrderType(
                    $carts[0]['invoiceReturnId'],
                    $totalProductInThisInvoice - $totalReturnedProduct == $totalCartProduct ? 'fully' : 'partial',
                    $orderType
                );

                $orders['order_type'] = $orderType;
            }

            if ($salesOrReceivingType == 'internal') {
                $orders['transfer_branch_id'] = $transferBranch;
            }


            $orderType == 'sales' ? $orders['customer_id'] = $id : $orders['supplier_id'] = $id;

            $orders['created_by'] = $createdBy;

            if ($orders['table_id']) {
                RestaurantTable::updateTableStatus($orders['table_id'], 'available');
            }

            Order::updateData($request->orderID, $orders);

            if ($request->shippingAreaId != null && $orderStatus == 'done') {
                $setShippingInfo = $this->storeShippingInformation($request, $request->orderID);
            }
            if ($salesOrReceivingType == 'internal-transfer') {
                $orders['order_type'] = 'receiving';
                Order::updateData($request->orderIdInternalTransfer, $orders);
            }
        }
        $orderItems = [];
        $orderItemsInternalTransfer = [];

        foreach ($carts as $cart) {
            $orderType == 'sales' ? $quantity = -$cart['quantity'] : $quantity = $cart['quantity'];

            if (!array_key_exists('discount', $cart) || $cart['discount'] == null) {
                $cart['discount'] = 0;
            }

            $ordered_quantity =0;
            $grn_status = "done";
            if( $orderType == 'receiving'){
                $ordered_quantity = $quantity;
                $quantity = 0;
                $grn_status = "receiving";
            }

            array_push($orderItems, [
                'product_id' => $cart['productID'],
                'variant_id' => $cart['variantID'],
                'type' => $cart['orderType'],
                'quantity' => $quantity,
                'ordered_quantity' => $ordered_quantity,
                'price' => $cart['price'],
                'discount' => $cart['discount'],
                'sub_total' => $cart["calculatedPrice"],
                'tax_id' => $cart['taxID'],
                'order_id' => $orderID,
                'note' => $cart['cartItemNote'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'grn_status' => $grn_status,
            ]);

            // for update isNotify product_variant
            if (isset($cart['variantID'])) {
                ProductVariant::removeBranchFromIsNotify($cart['variantID'], $request->branchId);
            }

            if ($salesOrReceivingType == 'internal-transfer') {
                $quantity = $cart['quantity'];
                array_push($orderItemsInternalTransfer, [
                    'product_id' => $cart['productID'],
                    'variant_id' => $cart['variantID'],
                    'type' => 'receiving',
                    'quantity' => $quantity,
                    'price' => $cart['price'],
                    'discount' => $cart['discount'],
                    'sub_total' => $cart["calculatedPrice"],
                    'tax_id' => $cart['taxID'],
                    'order_id' => $orderIdInternalTransfer,
                    'note' => $cart['cartItemNote'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
        }

        if ($orderStatus != 'hold') {
            if (sizeof($payment) > 0) {
                $paymentArray = [];
                $paymentArrayInternal = [];

                foreach ($payment as $rowPayment) {
                    array_push($paymentArray, ['date' => $date, 'paid' => $rowPayment['paid'], 'exchange' => $rowPayment['exchange'], 'payment_method' => $rowPayment['paymentID'], 'options' => serialize($rowPayment['options']), 'order_id' => $orderID, 'cash_register_id' => $cashRegisterID, 'is_active' => $rowPayment['is_active'], 'created_at' => $rowPayment['PaymentTime']]);
                }
                foreach ($payment as $rowPayment) {
                    array_push($paymentArrayInternal, ['date' => $date, 'paid' => $rowPayment['paid'], 'exchange' => $rowPayment['exchange'], 'payment_method' => $rowPayment['paymentID'], 'options' => serialize($rowPayment['options']), 'order_id' => $orderIdInternalTransfer, 'cash_register_id' => $cashRegisterID, 'is_active' => $rowPayment['is_active'], 'created_at' => $rowPayment['PaymentTime']]);
                }

                if (($orderStatus == 'done' && !$orderID) || ($orderStatus == 'pending' && !$orderID)) {
                    Payments::insertData($paymentArray);
                } else {
                    Payments::deleteRecord('order_id', $request->orderID);
                    Payments::insertData($paymentArray);
                    if ($salesOrReceivingType == 'internal-transfer') {
                        Payments::deleteRecord('order_id', $orderIdInternalTransfer);
                        Payments::insertData($paymentArrayInternal);
                    }
                }
            }
        }

        if ($orderType == 'sales') {
            $invoiceId = $invoiceFixes['prefix'] . $invoiceFixes['lastInvoiceNumber'] . $invoiceFixes['suffix'];
        } else $invoiceId = $invoiceFixes['purchasePrefix'] . $invoiceFixes['purchaseLastInvoiceNumber'] . $invoiceFixes['purchaseSuffix'];


        if (($orderStatus == 'done' && $orderID == null)) {
            OrderItems::insertData($orderItems);
            $response = [
                'invoiceID' => $invoiceId,
            ];
            return $response;
        } else if (($orderStatus == 'pending' && $orderID == null)) {
            OrderItems::insertData($orderItems);
            $response = [
                'orderID' => $orderID,
                'orderIdInternalTransfer' => $orderIdInternalTransfer
            ];
            return $response;
        } else {
            OrderItems::deleteRecord('order_id', $request->orderID);
            OrderItems::insertData($orderItems);
            if ($salesOrReceivingType == 'internal-transfer') {
                OrderItems::insertData($orderItemsInternalTransfer);
            }
            if ($orderType == 'sales') {
                $invoiceId = $invoiceFixes['prefix'] . $invoiceFixes['lastInvoiceNumber'] . $invoiceFixes['suffix'];
                $lastInvoiceId = Setting::getSettingValue('last_invoice_number')->setting_value;
            } else {
                $invoiceId = $invoiceFixes['purchasePrefix'] . $invoiceFixes['purchaseLastInvoiceNumber'] . $invoiceFixes['purchaseSuffix'];
                $lastInvoiceId = Setting::getSettingValue('purchase_last_invoice_number')->setting_value;
            }

            if ($orderStatus == 'done') {
                // send customer invoice
                try {

                    $invoiceTemplateEmail = new InvoiceTemplateController();
                    $invoiceTemplateData = $invoiceTemplateEmail->getInvoiceTemplateToPrint($orderID, $salesOrReceivingType, $transferBranchName, $cashRegister, $orderType, 'email');

                    $autoEmailReceive = Setting::getSettingValue('auto_email_receive')->setting_value;
                    
                    // \DB::enableQueryLog();
                    $orderDetails = Order::orderDetails($orderID, $cashRegister);
                    // dd(\DB::getQueryLog());
                    // Sms receive to customer
                    $autoSmsReceive = Setting::getSettingValue('sms_recive_to_customer')->setting_value;

                

                    $orderCustomerState  = null;
                    if (isset($orderDetails->customer_id) &&  $orderDetails->customer_id != null) {
                        $orderCustomer = Customer::getOne($orderDetails->customer_id);

                        $orderCustomerState = $orderCustomer->state;

                        if ($autoSmsReceive == 1 && $orderCustomer->phone_number) {
                            $this->autoCustomerSmsSend($orderCustomer->first_name, $orderCustomer->last_name, $orderCustomer->phone_number, $orderDetails->invoice_id, $orderDetails->total);

                        }

                        if ($autoEmailReceive == 1 && $orderCustomer->email) {

                            $content = EmailTemplate::query()->select('template_subject', 'default_content', 'custom_content')->where('template_type', 'pos_invoice')->first();

                            $subject = $content->template_subject;
                            $text = $content->custom_content ?  $content->custom_content : $content->default_content;

                            $mailText = str_replace('{first_name}', $orderCustomer->first_name, str_replace('{invoice_id}', $orderDetails->invoice_id, str_replace('{app_name}', Config::get('app_name'), $text)));

                            $this->sendPdf($invoiceTemplateData['data'], $orderID, $cashRegister, $mailText, $orderCustomer->email, $subject);

                        }
                    }
                    if(isset($orderDetails->supplier_id) && $orderDetails->supplier_id != null && $orderType == 'receiving'){
                        $orderCustomer = Supplier::getOne($orderDetails->supplier_id);

                        $orderCustomerState = '25';//Telangana

                        if ($autoSmsReceive == 1 && $orderCustomer->phone_number) {
                            $this->autoCustomerSmsSend($orderCustomer->first_name, $orderCustomer->last_name, $orderCustomer->phone_number, $orderDetails->invoice_id, $orderDetails->total);

                        }

                        if ($autoEmailReceive == 1 && $orderCustomer->email) {

                            $content = EmailTemplate::query()->select('template_subject', 'default_content', 'custom_content')->where('template_type', 'pos_invoice')->first();

                            $subject = $content->template_subject;
                            $text = $content->custom_content ?  $content->custom_content : $content->default_content;

                            $mailText = str_replace('{first_name}', $orderCustomer->first_name, str_replace('{invoice_id}', $orderDetails->invoice_id, str_replace('{app_name}', Config::get('app_name'), $text)));

                            $this->sendPdf($invoiceTemplateData['data'], $orderID, $cashRegister, $mailText, $orderCustomer->email, $subject);

                        }
                    }

                } catch (\Exception $e) {
                    return $e;
                }

                $invoiceTemplate = new InvoiceTemplateController();

                $templateData = $invoiceTemplate->getInvoiceTemplateToPrint($orderID, $salesOrReceivingType, $transferBranchName, $cashRegister, $orderType, 'receipt');
                if($orderType == "receiving"){
                    $productDetails = $this->getOrderItemssummary($orderID);
                    $productData = "";
                    $slNO = 1;
                    foreach ($productDetails['data'] as $item) {
                        $productData .= "<tr>";
                        $productData .= "<tr><td>".$slNO++."</td>";
                        $productData .= "<td>".$item->title."</td>";
                        $productData .= "<td>".$item->short_name."</td>";
                        $productData .= "<td>".$item->orderquantity."</td>";
                        $productData .= "<td>".$item->price."</td>";
                        $productData .= "<td>".$item->total_price."</td>";
                        $productData .= "</tr>";
                    }
                    $templateData = str_replace('{product_details}', $productData, $templateData);
                    $totalAmountInWords = currencyToWords($grandTotal, 'INR');
                    $roundoff = round(abs(floor($orderDetails->total) - ((float)$orderDetails->total_tax + (float)$orderDetails->sub_total)), 2);
                    $templateData = str_replace('{roundoff}',  number_format($roundoff, 2), $templateData);
                    $total = floor($orderDetails->total);
                    $templateData = str_replace('{total_sum}',  $allSettings->getCurrency(number_format($total, 2)), $templateData);
                    $templateData = str_replace('{total_qty}', $total_qty, $templateData);
                    $templateData = str_replace('{tax}', $tax, $templateData);
                    $templateData = str_replace('{sub_total}', number_format($subTotal, 2), $templateData);
                    $templateData = str_replace('{customer_name}', $orderCustomer ? $orderCustomer->first_name.$orderCustomer->last_name : '', $templateData);
                    $templateData = str_replace('{customer_phone}', $orderCustomer ? $orderCustomer->phone_number : '', $templateData);
                    $taxPercentage = ($subTotal != 0) ? ($tax / $subTotal) * 100 : 0;
                    $templateData = str_replace('{gst_percentage}', number_format($taxPercentage, 2), $templateData);
                    $templateData = str_replace('{totalAmountInWords}', $totalAmountInWords, $templateData);
                }
                $clientState = Config::get('client_location_state');
                $gstType = $clientState == $orderCustomerState ? 'SGST' : 'IGST';
                $gstType = is_null($orderCustomerState) ? 'SGST' : 'IGST';
                

                $response = [
                    'orderID' => $orderID,
                    'orderIdInternalTransfer' => $orderIdInternalTransfer,
                    'invoiceID' => $invoiceId,
                    'message' => Lang::get('lang.payment_done_successfully'),
                    'invoiceTemplate' => $templateData,
                    'orderCustomerState' => $orderCustomerState,
                    'clientState' => $clientState,
                    'gstType' => $gstType,
                    'lastInvoiceId' => $lastInvoiceId,
                    'time' => date('d/m/Y h:i a')
                ];

                return $response;
            } else {
                $response = [
                    'orderID' => $orderID,
                    'orderIdInternalTransfer' => $orderIdInternalTransfer,
                    'invoiceID' => $invoiceId,
                    'message' => Lang::get('lang.payment_done_successfully'),
                    'lastInvoiceId' => $lastInvoiceId,
                ];

                return $response;
            }
        }
    }
    
   public static function getOrderItemssummary($orderId){
        $productDetailsQuery = DB::table('order_items as oi')
            ->join('products as p', 'oi.product_id', '=', 'p.id')
            ->join('product_categories as pc', 'p.category_id', '=', 'pc.id')
            ->join('product_units as pu','p.unit_id', '=', 'pu.id')
            ->where('oi.order_id', $orderId)
            ->groupBy('title')
            ->select(
                DB::raw("CONCAT(pc.name, ' - ', p.title) as title"),
                'pu.short_name',
                DB::raw('SUM(oi.ordered_quantity) as orderquantity'),
                DB::raw('SUM(oi.quantity) as quantity'),
                'oi.price',
                DB::raw('SUM(oi.ordered_quantity * oi.price) as total_price')
            )
            ->orderBy('p.title')
            ->get();
        $order = Order::query()
            ->where('id', '=', $orderId)
            ->select('total_tax','total','sub_total')
            ->get();
        return ['data' => $productDetailsQuery, 'order' => $order];
       
    }

    public static function getOrderItemssummary1($orderId)
   {
        $productDetailsQuery = DB::table('order_items as oi')
            ->join('products as p', 'oi.product_id', '=', 'p.id')
            ->join('product_categories as pc', 'p.category_id', '=', 'pc.id')
            ->join('product_units as pu','p.unit_id', '=', 'pu.id')
            ->where('oi.order_id', $orderId)
            ->groupBy('title')
            ->select(
                DB::raw("CONCAT(pc.name, ' - ', p.title) as title"),
                'pu.short_name',
                DB::raw('SUM(oi.ordered_quantity) as orderquantity'),
                'oi.price',
                // 'oi.quantity as receivedquantity',
                DB::raw('SUM(oi.quantity) as receivedquantity'),
                DB::raw('SUM(oi.ordered_quantity * oi.price) as total_price')
            )
            ->orderBy('title')
            ->get();
        $order = Order::query()
            ->where('id', '=', $orderId)
            ->select('total_tax','total','sub_total', 'grn_invoice', 'sales_note')
            ->get();
        return ['data' => $productDetailsQuery, 'order' => $order];
    }

    public static function getOrderItemssummaryDetails($orderId)
   {
        $productDetailsQuery = DB::table('order_items as oi')
            ->join('products as p', 'oi.product_id', '=', 'p.id')
            ->join('product_variants as pv','pv.id', '=', 'oi.variant_id')
            ->join('product_categories as pc', 'p.category_id', '=', 'pc.id')
            ->join('product_units as pu','p.unit_id', '=', 'pu.id')
            ->where('oi.order_id', $orderId)
            ->groupBy('title')
            ->select(
                DB::raw("CONCAT(pc.name, ' - ', p.title) as title"),
                'pu.short_name',
                DB::raw('SUM(oi.ordered_quantity) as orderquantity'),
                'oi.price',
                // 'oi.quantity as receivedquantity',
                DB::raw('SUM(oi.quantity) as receivedquantity'),
                DB::raw('SUM(oi.ordered_quantity * oi.price) as total_price'),
                'pc.name as course',
                'p.title as item',
                'pv.variant_title as item_size'

            )
            ->orderBy('title')
            ->get();
        
        return $productDetailsQuery;
    }
 
    
    public function salesStoreUpdate(Request $request)
    {
        $requested_id = collect($request->cart)->pluck('productID');
        $cashRegister = $request->cashRagisterId;
        $allSettings = new AllSettingFormat;
        $userId = Auth::id();
        $userBranchId = Setting::getFirst('*', 'user_id', $userId)->setting_value;
        $date = Carbon::now()->toDateString();
        $orderType = $request->orderType;
        $salesOrReceivingType = $request->salesOrReceivingType;
        $orderStatus = $request->status;
        $createdBy = Auth::user()->id;
        $carts = $request->cart;
        $id = $request->customer ? $request->customer['id'] : null;
        $sales_person_id = $request->salesPerson ? $request->salesPerson['id'] : 0;
        $subTotal = $request->subTotal;
        $tax = $request->tax;
        $allDiscount = $request->discount; //percentage discount on entire sell
        $grandTotal = $request->grandTotal;
        $payment = isset($request->payments) ? $request->payments : [];
        $orderID = $request->orderID;
        $orderIdInternalTransfer = $request->orderIdInternalTransfer;
        $transferBranch = $request->transferBranch;
        $transferBranchName = $request->transferBranchName;
        $dueAmount = 0;
        $time = $request->time;
        $restaurantTableId = $request->tableId;
        $variantTitle = '';
        $message = [];
        $orderData1 = [];
        $allOrderData1 = [];
        $totalProductInThisInvoice = 0;
        $totalReturnedProduct = 0;
        $totalCartProduct = 0;
        $returnProductProfit = 0;

        /*Section for Return Order product in cart*/
        if ($request->salesOrReturnType == 'returns') {
            $orderItemProductCount = 0;
            //checking cart with discount
            foreach ($carts as $cart) if ($cart['orderType'] == 'sales') $orderItemProductCount++;

            $salingProfit = Order::getFirst(['id', 'profit'], 'invoice_id', $request->cart[0]['invoiceReturnId']);

            $returnInvoiceProfit = Order::getReturnProductProfit($carts[0]['invoiceReturnId']);

            if ($returnInvoiceProfit->profit == null) $returnInvoiceProfit->profit = 0;

            $productsSoldFirstCount = OrderItems::countRecord('order_id', $salingProfit->id);
        }
        /*Section for Return Order product in cart ends*/

        $totalCartProduct = (collect($carts)->sum('quantity')) * (-1);


        if ($grandTotal < 0) {

            $orderDetailsInformation = Order::getOrderInformation($carts[0]['invoiceReturnId'], $orderType);

            if (count($orderDetailsInformation) > 0) $returnProductProfit = $orderDetailsInformation[0]->profit; //checking profit while return product

            $totalProductInThisInvoice = (collect($orderDetailsInformation)->sum('quantity')) * (-1);

            $diffQuantity = $orderDetailsInformation
                ->whereIn('product_id', $requested_id)
                ->filter(function ($order_item) use ($request) {
                    $item = collect($request->cart)->first(function ($item) use ($order_item) {
                        return $item["productID"] == $order_item->product_id;
                    });
                    $order_item->quantity_difference = $item["quantity"] - $order_item->quantity;
                    return $item["quantity"] > $order_item->quantity;
                });

            $orderData1 = $orderDetailsInformation->whereNotIn('product_id', $requested_id);
            $allOrderData1 = collect($orderData1)->merge($diffQuantity)->map(function ($order) {
                $item["product_id"] = $order->product_id;
                $item["variant_id"] = $order->variant_id;
                if ($item["quantity"] = $order->quantity_difference == null) {
                    $item["quantity"] = $order->quantity * -1;
                } else {
                    $item["quantity"] = $order->quantity_difference;
                }
                return $item;
            });
        }


        $outOfStock = Setting::getSettingValue('out_of_stock_products')->setting_value;
        $checkAvailableQuantity = 'false';

        foreach ($request->cart as $cart) {
            $availableQuantityCheck = OrderItems::checkAvailableQuantity($cart['variantID']);

            if ($cart['orderType'] !== 'discount') {
                $outOfStockVariantTitle = $cart['variantTitle'] ? ' (' . $cart['variantTitle'] . ') ' : ' ' . $cart['variantTitle'] . ' ';
                if ($outOfStock == 1 && $request->orderType == 'sales' && $request->status == 'done' && $cart['quantity'] > $availableQuantityCheck && $cart['quantity'] > 0) {
                    $checkAvailableQuantity = 'true';
                    if ($availableQuantityCheck <= 0) {
                        array_push($message, $cart['productTitle'] . $outOfStockVariantTitle . trans('lang.is_out_of_stock') . ' ' . trans('lang.please_remove_from_cart'));
                    } else {
                        array_push($message, $cart['productTitle'] . $outOfStockVariantTitle . trans('lang.is_out_of_stock') . ' ' . trans('lang.available_quantity') . '' . $availableQuantityCheck . '.');
                    }
                }
            }


            if ($orderType == 'receiving'
                || $salesOrReceivingType == 'internal'
                || $salesOrReceivingType == 'internal-transfer') {
                // $this->updateVariantPurchasePrice($cart);
            } else {
                // $this->updateVariantSalesPrice($cart);
            }
        }

        if ($checkAvailableQuantity == 'true') {

            $response = [
                'checkAvailableQuantity' => $checkAvailableQuantity,
                'message' => $message
            ];
            return response()->json($response, 200);
        }
        $lastInvoiceNumber = Setting::getSettingValue('last_invoice_number')->setting_value;
        $purchaseLastInvoiceNumber = Setting::getSettingValue('purchase_last_invoice_number')->setting_value;

        $profit = $request->profit == null ? 0 : $request->profit;

        $invoiceFixes = $allSettings->getInvoiceFixes();

        if ($allSettings->getCurrentBranch()->is_cash_register == 1) {
            $cashRegisterID = $this->getCashRegisterID()->id;
        } else {
            $cashRegisterID = null;
        }

        if ($allDiscount == null) {
            $allDiscount = 0;
        }

        if (!empty($payment)) {
            foreach ($payment as $key => $value) {
                if ($value['paymentType'] == 'credit') {
                    $dueAmount = floatval($value['paid']);
                }
            }
        }
        if (($orderStatus == 'done' && !$orderID) || ($orderStatus == 'pending' && !$orderID) || ($orderStatus == 'hold' && !$orderID)) {
            $orderData = array();
            $orderData['date'] = $date;
            $orderData['sales_note'] = $request->salesNote;
            $orderData['all_discount'] = $allDiscount;
            $orderData['sub_total'] = $subTotal;
            $orderData['total_tax'] = $tax;
            $orderData['due_amount'] = $dueAmount;
            $orderData['total'] = $grandTotal;
            $orderData['type'] = $salesOrReceivingType;
            $orderData['profit'] = $profit;
            $orderData['status'] = $orderStatus;
            $orderData['table_id'] = $restaurantTableId;
            $orderData['sales_user_id'] = $sales_person_id; 

            if ($orderData['total'] >= 0 || $salesOrReceivingType == "internal-transfer") {
                $orderData['order_type'] = $orderType;
            } else {
                //return product section

                $getReturnProduct = Order::getReturnProduct($carts[0]['invoiceReturnId'], $orderType);

                $totalReturnedProduct = collect($getReturnProduct)->sum('quantity');

                if (count($allOrderData1) > 0) {
                    if ($orderType == 'receiving') {
                        $totalProductInThisInvoice = $totalProductInThisInvoice * -1;
                    }
                    if ($totalProductInThisInvoice - $totalReturnedProduct == $totalCartProduct) {
                        $returnType = 'fully';
                    } else {
                        $returnType = 'partial';
                    }
                } else {
                    $returnType = 'fully';
                }

                $orderData['order_type'] = $orderType;

                $orderData['type'] = $salesOrReceivingType;
                $orderData['returned_invoice'] = $carts[0]['invoiceReturnId'];

                /*profit calculation for return product*/
                if ($request->salesOrReturnType == 'returns') {
                    if ($returnType == 'fully') {
                        $orderData['profit'] = ($returnProductProfit - $returnInvoiceProfit->profit) * (-1);
                    } else {
                        $availableProfit = $salingProfit->profit - $returnInvoiceProfit->profit;
                        $orderData['profit'] = $availableProfit / $productsSoldFirstCount * (-1);
                    }
                } else {
                    //for purchase return
                    $orderData['profit'] = 0;
                }
            }

            if ($salesOrReceivingType == 'internal' || $salesOrReceivingType == 'internal-transfer') {
                $orderData['transfer_branch_id'] = $transferBranch;
            }

            $orderType === 'sales' ? $orderData['customer_id'] = $id : $orderData['supplier_id'] = $id;

            $orderData['created_by'] = $createdBy;
            $orderData['branch_id'] = $userBranchId;
            $orderData['created_at'] = Carbon::parse($time);

            if ($orderData['table_id']) {
                RestaurantTable::updateTableStatus($orderData['table_id'], 'booked');
            }

            $orderLastId = Order::store($orderData);


            if ($salesOrReceivingType == 'internal-transfer') {
                $orderIdInternalTransfer = $this->insertInternalTransfer($orderData, $transferBranch, $userBranchId, $invoiceFixes, $purchaseLastInvoiceNumber);
            }

            if ($orderLastId->total < 0 && $orderLastId->status == 'done') {
                Order::updateOrderType($carts[0]['invoiceReturnId'], $returnType, $orderType);
            }


            if ($request->shippingAreaId != null && $orderStatus == 'done') {
                $setShippingInfo = $this->storeShippingInformation($request, $orderLastId->id);
            }

            $orderID = $orderLastId->id;

            if ($orderLastId->order_type == 'sales') {                
                Order::updateData($orderID, ['invoice_id' => $invoiceFixes['prefix'] . $lastInvoiceNumber . $invoiceFixes['suffix']]);
                $lastInvoiceNumber += 1;

                $lastUpdatedInvoice = Setting::where('setting_name', 'last_invoice_number')->first()->setting_value;
                if ($lastInvoiceNumber > $lastUpdatedInvoice) {
                    Setting::updateSetting('last_invoice_number', $lastInvoiceNumber);
                }
            } else {
                Order::updateData($orderID, ['invoice_id' => $invoiceFixes['purchasePrefix'] . $purchaseLastInvoiceNumber . $invoiceFixes['purchaseSuffix']]);
                $purchaseLastInvoiceNumber += 1;

                $purchaseLastUpdatedInvoice = Setting::where('setting_name', 'purchase_last_invoice_number')->first()->setting_value;
                if ($purchaseLastInvoiceNumber > $purchaseLastUpdatedInvoice) {
                    Setting::updateSetting('purchase_last_invoice_number', $purchaseLastInvoiceNumber);
                }
            }
            
        } else {
            $orders = array();
            $orders['date'] = $date;
            $orders['sales_note'] = $request->salesNote;
            $orders['order_type'] = $orderType;
            $orders['all_discount'] = $allDiscount;
            $orders['sub_total'] = $subTotal;
            $orders['total_tax'] = $tax;
            $orders['total'] = $grandTotal;
            $orders['type'] = $salesOrReceivingType;
            $orders['status'] = $orderStatus;
            $orders['table_id'] = $restaurantTableId;
            $orders['due_amount'] = $dueAmount;
            $orderData['sales_user_id'] = $sales_person_id; 

            if ($orders['total'] < 0) {
                $getReturnProduct = Order::getReturnProduct($carts[0]['invoiceReturnId'], $orderType);
                $totalReturnedProduct = collect($getReturnProduct)->sum('quantity');

                if ($orderType == 'receiving') {
                    $totalProductInThisInvoice = $totalProductInThisInvoice * -1;
                }

                Order::updateOrderType(
                    $carts[0]['invoiceReturnId'],
                    $totalProductInThisInvoice - $totalReturnedProduct == $totalCartProduct ? 'fully' : 'partial',
                    $orderType
                );

                $orders['order_type'] = $orderType;
            }

            if ($salesOrReceivingType == 'internal') {
                $orders['transfer_branch_id'] = $transferBranch;
            }


            $orderType == 'sales' ? $orders['customer_id'] = $id : $orders['supplier_id'] = $id;

            $orders['created_by'] = $createdBy;

            if ($orders['table_id']) {
                RestaurantTable::updateTableStatus($orders['table_id'], 'available');
            }
            Order::updateData($request->orderID, $orders);

            if ($request->shippingAreaId != null && $orderStatus == 'done') {
                $setShippingInfo = $this->storeShippingInformation($request, $request->orderID);
            }
            if ($salesOrReceivingType == 'internal-transfer') {
                $orders['order_type'] = 'receiving';
                Order::updateData($request->orderIdInternalTransfer, $orders);
            }
        }
        $orderItems = [];
        $orderItemsInternalTransfer = [];

        foreach ($carts as $cart) {
            $orderType == 'sales' ? $quantity = -$cart['quantity'] : $quantity = $cart['quantity'];

            if (!array_key_exists('discount', $cart) || $cart['discount'] == null) {
                $cart['discount'] = 0;
            }

            array_push($orderItems, [
                'product_id' => $cart['productID'],
                'variant_id' => $cart['variantID'],
                'type' => $cart['orderType'],
                'quantity' => 0,
                'ordered_quantity' => $quantity,
                'price' => $cart['price'],
                'discount' => $cart['discount'],
                'sub_total' => $cart["calculatedPrice"],
                'tax_id' => $cart['taxID'],
                'order_id' => $orderID,
                'note' => $cart['cartItemNote'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // for update isNotify product_variant
            if (isset($cart['variantID'])) {
                ProductVariant::removeBranchFromIsNotify($cart['variantID'], $request->branchId);
            }

            if ($salesOrReceivingType == 'internal-transfer') {
                $quantity = $cart['quantity'];
                array_push($orderItemsInternalTransfer, [
                    'product_id' => $cart['productID'],
                    'variant_id' => $cart['variantID'],
                    'type' => 'receiving',
                    'quantity' => $quantity,
                    'price' => $cart['price'],
                    'discount' => $cart['discount'],
                    'sub_total' => $cart["calculatedPrice"],
                    'tax_id' => $cart['taxID'],
                    'order_id' => $orderIdInternalTransfer,
                    'note' => $cart['cartItemNote'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
        }

        if ($orderStatus != 'hold') {
            if (sizeof($payment) > 0) {
                $paymentArray = [];
                $paymentArrayInternal = [];

                foreach ($payment as $rowPayment) {
                    array_push($paymentArray, ['date' => $date, 'paid' => $rowPayment['paid'], 'exchange' => $rowPayment['exchange'], 'payment_method' => $rowPayment['paymentID'], 'options' => serialize($rowPayment['options']), 'order_id' => $orderID, 'cash_register_id' => $cashRegisterID, 'is_active' => $rowPayment['is_active'], 'created_at' => $rowPayment['PaymentTime']]);
                }
                foreach ($payment as $rowPayment) {
                    array_push($paymentArrayInternal, ['date' => $date, 'paid' => $rowPayment['paid'], 'exchange' => $rowPayment['exchange'], 'payment_method' => $rowPayment['paymentID'], 'options' => serialize($rowPayment['options']), 'order_id' => $orderIdInternalTransfer, 'cash_register_id' => $cashRegisterID, 'is_active' => $rowPayment['is_active'], 'created_at' => $rowPayment['PaymentTime']]);
                }

                if (($orderStatus == 'done' && !$orderID) || ($orderStatus == 'pending' && !$orderID)) {
                    Payments::insertData($paymentArray);
                } else {
                    Payments::deleteRecord('order_id', $request->orderID);
                    Payments::insertData($paymentArray);
                    if ($salesOrReceivingType == 'internal-transfer') {
                        Payments::deleteRecord('order_id', $orderIdInternalTransfer);
                        Payments::insertData($paymentArrayInternal);
                    }
                }
            }
        }

        if ($orderType == 'sales') {
            $invoiceId = $invoiceFixes['prefix'] . $invoiceFixes['lastInvoiceNumber'] . $invoiceFixes['suffix'];
        } else $invoiceId = $invoiceFixes['purchasePrefix'] . $invoiceFixes['purchaseLastInvoiceNumber'] . $invoiceFixes['purchaseSuffix'];
        
        if (($orderStatus == 'done' && $orderID == null)) {
            OrderItems::insertData($orderItems);    
            $response = [
                'invoiceID' => $invoiceId,
            ];
            return $response;
        } else if (($orderStatus == 'pending' && $orderID == null)) {
            OrderItems::insertData($orderItems);
            $response = [
                'orderID' => $orderID,
                'orderIdInternalTransfer' => $orderIdInternalTransfer
            ];
            return $response;
        } else {
            // OrderItems::deleteRecord('order_id', $request->orderID);
            // for data insert and update if exists
            foreach ($request->cart as $item) {
                $product_exists = OrderItems::where('order_id', $request->orderID)
                                            ->where('product_id', $item['productID'])
                                            ->where('variant_id', $item['variantID'])->exists();
                if ($product_exists) {
                    OrderItems::where('order_id', $request->orderID)
                        ->where('product_id', $item['productID'])
                        ->where('variant_id', $item['variantID'])
                        ->update(['ordered_quantity' => $item['quantity'], 'price' => $item['price']]);
                } else {
                    $data = [
                        'order_id' => $request->orderID,
                        'product_id' => $item['productID'],
                        'variant_id' => $item['variantID'], 
                        'type' => $item['orderType'],
                        'ordered_quantity' => $item['quantity'],
                        'price' => $item['price'],
                        'discount' => $item['discount']
                    ];  
                    OrderItems::create($data);
                }
            
            
            }
            
            // OrderItems::insertData($orderItems);
            if ($salesOrReceivingType == 'internal-transfer') {
                OrderItems::insertData($orderItemsInternalTransfer);
            }
            if ($orderType == 'sales') {
                $invoiceId = $invoiceFixes['prefix'] . $invoiceFixes['lastInvoiceNumber'] . $invoiceFixes['suffix'];
                $lastInvoiceId = Setting::getSettingValue('last_invoice_number')->setting_value;
            } else {
                $invoiceId = $invoiceFixes['purchasePrefix'] . $invoiceFixes['purchaseLastInvoiceNumber'] . $invoiceFixes['purchaseSuffix'];
                $lastInvoiceId = Setting::getSettingValue('purchase_last_invoice_number')->setting_value;
            }

            if ($orderStatus == 'done') {
                // send customer invoice
                try {

                    $invoiceTemplateEmail = new InvoiceTemplateController();
                    $invoiceTemplateData = $invoiceTemplateEmail->getInvoiceTemplateToPrint($orderID, $salesOrReceivingType, $transferBranchName, $cashRegister, $orderType, 'email');

                    $autoEmailReceive = Setting::getSettingValue('auto_email_receive')->setting_value;
                    
                    // \DB::enableQueryLog();
                    $orderDetails = Order::orderDetails($orderID, $cashRegister);
                    // dd(\DB::getQueryLog());
                    // Sms receive to customer
                    $autoSmsReceive = Setting::getSettingValue('sms_recive_to_customer')->setting_value;

                

                    $orderCustomerState  = null;
                    if (isset($orderDetails->customer_id) &&  $orderDetails->customer_id != null) {
                        $orderCustomer = Customer::getOne($orderDetails->customer_id);

                        $orderCustomerState = $orderCustomer->state;

                        if ($autoSmsReceive == 1 && $orderCustomer->phone_number) {
                            $this->autoCustomerSmsSend($orderCustomer->first_name, $orderCustomer->last_name, $orderCustomer->phone_number, $orderDetails->invoice_id, $orderDetails->total);

                        }

                        if ($autoEmailReceive == 1 && $orderCustomer->email) {

                            $content = EmailTemplate::query()->select('template_subject', 'default_content', 'custom_content')->where('template_type', 'pos_invoice')->first();

                            $subject = $content->template_subject;
                            $text = $content->custom_content ?  $content->custom_content : $content->default_content;

                            $mailText = str_replace('{first_name}', $orderCustomer->first_name, str_replace('{invoice_id}', $orderDetails->invoice_id, str_replace('{app_name}', Config::get('app_name'), $text)));

                            $this->sendPdf($invoiceTemplateData['data'], $orderID, $cashRegister, $mailText, $orderCustomer->email, $subject);

                        }
                    }
                    if(isset($orderDetails->supplier_id) && $orderDetails->supplier_id != null && $orderType == 'receiving'){
                        $orderCustomer = Supplier::getOne($orderDetails->supplier_id);

                        $orderCustomerState = '25';//Telangana

                        if ($autoSmsReceive == 1 && $orderCustomer->phone_number) {
                            $this->autoCustomerSmsSend($orderCustomer->first_name, $orderCustomer->last_name, $orderCustomer->phone_number, $orderDetails->invoice_id, $orderDetails->total);

                        }

                        if ($autoEmailReceive == 1 && $orderCustomer->email) {

                            $content = EmailTemplate::query()->select('template_subject', 'default_content', 'custom_content')->where('template_type', 'pos_invoice')->first();

                            $subject = $content->template_subject;
                            $text = $content->custom_content ?  $content->custom_content : $content->default_content;

                            $mailText = str_replace('{first_name}', $orderCustomer->first_name, str_replace('{invoice_id}', $orderDetails->invoice_id, str_replace('{app_name}', Config::get('app_name'), $text)));

                            $this->sendPdf($invoiceTemplateData['data'], $orderID, $cashRegister, $mailText, $orderCustomer->email, $subject);

                        }
                    }

                } catch (\Exception $e) {
                    return $e;
                }

                $invoiceTemplate = new InvoiceTemplateController();
                $templateData = $invoiceTemplate->getInvoiceTemplateToPrint($orderID, $salesOrReceivingType, $transferBranchName, $cashRegister, $orderType, 'receipt');

                $clientState = Config::get('client_location_state');
                $gstType = $clientState == $orderCustomerState ? 'SGST' : 'IGST';
                $gstType = is_null($orderCustomerState) ? 'SGST' : 'IGST';
                

                $response = [
                    'orderID' => $orderID,
                    'orderIdInternalTransfer' => $orderIdInternalTransfer,
                    'invoiceID' => $invoiceId,
                    'message' => Lang::get('lang.payment_done_successfully'),
                    'invoiceTemplate' => $templateData,
                    'orderCustomerState' => $orderCustomerState,
                    'clientState' => $clientState,
                    'gstType' => $gstType,
                    'lastInvoiceId' => $lastInvoiceId,
                    'time' => date('d/m/Y h:i a')
                ];

                return $response;
            } else {
                $response = [
                    'orderID' => $orderID,
                    'orderIdInternalTransfer' => $orderIdInternalTransfer,
                    'invoiceID' => $invoiceId,
                    'message' => Lang::get('lang.payment_done_successfully'),
                    'lastInvoiceId' => $lastInvoiceId,
                ];

                return $response;
            }
        }
    }

    // auto sms

    protected function autoCustomerSmsSend($firstName, $lastName, $phoneNumber, $invoiceId, $total)
    {
        try {
            // Sms Template
            $smsText = SmsTemplate::select('template_subject', 'default_content', 'custom_content')->where('template_type', 'pos_sms')->first();
            if ($smsText->custom_content) {
                $text = $smsText->custom_content;
            } else {
                $text = $smsText->default_content;
            }
            $sendSmsText = str_replace('{first_name}', $firstName, str_replace('{last_name}', $lastName, str_replace('{invoice_id}', $invoiceId, str_replace('{total}', $total, str_replace('{app_name}', Config::get('app_name'), $text)))));
            $this->autoSmsSend($phoneNumber, $sendSmsText);
        } catch (\Exception $e) {
            $response = [
                'message' => Lang::get('lang.phone_number_wrong')
            ];

            return response()->json($response, 201);
        }
    }

    // Button sms

    public function customerSendSms(Request $request)
    {
        try {
            if ($request->id && $request->phone_number) {

                $smsText = SmsTemplate::select('template_subject', 'default_content', 'custom_content')->where('template_type', 'pos_sms')->first();
                if ($smsText->custom_content) {
                    $text = $smsText->custom_content;
                } else {
                    $text = $smsText->default_content;
                }
                $sendSmsText = str_replace('{first_name}', $request->first_name, str_replace('{last_name}', $request->last_name, str_replace('{invoice_id}', $request->invoiceId, str_replace('{total}', $request->subTotal, str_replace('{app_name}', Config::get('app_name'), $text)))));

                $this->autoSmsSend($request->phone_number, $sendSmsText);

                $response = [
                    'message' => Lang::get('lang.successfully_sms')
                ];

                return response()->json($response, 200);

            }
        } catch (\Exception $e) {
            $response = [
                'message' => Lang::get('lang.phone_number_wrong')
            ];

            return response()->json($response, 201);
        }


    }

    // auto sms receive customer
    protected function autoSmsSend($phone_number, $sendSmsText)
    {
        $send = SmsHelper::sendSms($phone_number, $sendSmsText);
        return $send;
    }

    public function saveDueAmount(Request $request)
    {

        $data = $request->cartItemsToStore;

        $orderId = $data['rowData']['id'];
        $paymentType = $data['paymentType'];
        $date = Carbon::now()->toDateString();
        $payments = $data['payments'];
        $cashRegisterID = null;
        $output = null;

        $allSettings = new AllSettingFormat;
        $userId = Auth::id();

        if ($allSettings->getCurrentBranch()->is_cash_register == 1) {
            $cashRegisterID = $this->getCashRegisterID()->id;
        } else {
            $cashRegisterID = null;
        }

        $deleteRow = Payments::destroyByOrderAndType($orderId, $paymentType);

        if (isset($payments)) {
            $paymentArray = [];
            $due = 0;
            foreach ($payments as $rowPayment) {
                array_push(
                    $paymentArray,
                    [
                        'date' => $date,
                        'paid' => $rowPayment['paid'],
                        'exchange' => $rowPayment['exchange'],
                        'payment_method' => $rowPayment['paymentID'],
                        'options' => serialize($rowPayment['options']),
                        'order_id' => $orderId,
                        'cash_register_id' => $cashRegisterID,
                        'created_at' => $rowPayment['PaymentTime']
                    ]
                );

                if ($rowPayment['paymentType'] == 'credit') {
                    $due = $rowPayment['paid'];
                }
            }
            $updateData = [
                'due_amount' => $due
            ];
            Order::updateData($orderId, $updateData);
            if (isset($paymentArray)) {
                $output = Payments::insertData($paymentArray);
            }
        }

        if ($output) {
            return [
                'orderID' => $orderId,
                'message' => Lang::get('lang.payment_done_successfully')
            ];
        } else {
            return [
                'orderID' => $orderId,
                'message' => Lang::get('lang.something_went_wrong')
            ];
        }
    }

    public function salesCancel(Request $request)
    {
        $orderId = $request->orderID;
        $cancel_reason = $request->cancel_reason;
        $orderIdInternalTransfer = $request->orderIdInternalTransfer;

        if (Order::checkExists('id', $orderId)) {
            Order::updateData($orderId, ['status' => 'cancelled', 'cancel_reason' => $cancel_reason]);
            if(PurchaseQuotation::checkExists('order_id', $orderId)){
                PurchaseQuotation::where('order_id', $orderId)->update(['status' => 'Cancelled']);
            }
        }
        if ($orderIdInternalTransfer != null) {
            if (Order::checkExists('id', $orderIdInternalTransfer)) {
                Order::updateData($orderIdInternalTransfer, ['status' => 'cancelled']);
            }
        }
    }

    public function getPaymentsAndDetails(Request $request)
    {
        $orderId = $request->orderID;
        $payments = [];

        if ($orderId) {
            $payments = Payments::getAll('*', 'order_id', $orderId);
        }

        return $payments;
    }

    public function customerList(Request $request)
    {
        $searchValue = $request->customerSearchValue;

        if ($request->orderType == 'sales') {

            return Customer::customerData($searchValue);
        } else {
            return Supplier::supplierData($searchValue);
        }
    }

    public function getHoldOrder()
    {
        $orderHoldItems = Order::getHoldOrders();

        //check if it return empty
        if (count($orderHoldItems) > 0) {
            foreach ($orderHoldItems as $rowOrderItem) {

                $allOrderItems = OrderItems::getAll(['price', 'discount', 'product_id as productID', 'type as orderType', 'tax_id as taxID', 'quantity', 'variant_id as variantID', 'note as cartItemNote'], 'order_id', $rowOrderItem->orderID);

                //check if it return empty
                if (count($allOrderItems) > 0) {
                    $rowOrderItem->cart = $allOrderItems;
                    foreach ($rowOrderItem->cart as $rowItem) {

                        if ($rowItem->taxID) {
                            $rowItem->productTaxPercentage = Tax::getFirst('percentage', 'id', $rowItem->taxID)->percentage;
                        } else {
                            $rowItem->productTaxPercentage = 0;
                        }

                        if ($rowItem->variantID != null) {

                            $rowItem->variantTitle = optional(ProductVariant::getFirst('variant_title', 'id', $rowItem->variantID))->variant_title;
                            $rowItem->productTitle = optional(Product::getFirst('title', 'id', $rowItem->productID))->title;
                        }

                        $rowItem->quantity = abs($rowItem->quantity);
                        $rowItem->showItemCollapse = false;
                        $rowItem->calculatedPrice = $rowItem->quantity * $rowItem->price;
                    }

                    //time as per settings
                    $rowOrderItem->time = Carbon::parse($rowOrderItem->date)->format('H:i:s');

                    if ($rowOrderItem->customer != null) {
                        $rowOrderItem->customer = Customer::getFirst(['first_name', 'last_name', 'email', 'id'], 'id', $rowOrderItem->customer);
                        $rowOrderItem->customer->customer_group_discount = 0;
                    }
                }
            }
        }

        return $orderHoldItems;
    }

    public function sendPdf($templateData, $orderID, $cashRegister, $mailText, $email, $subject)
    {
        try {

            $allSettingFormat = new AllSettingFormat();
            $order = $this->formatOrdersDetails($orderID, $cashRegister);
            $order->due = $allSettingFormat->getCurrencySeparator($order->due);
            $orderItems = $this->formatOrdersItems($orderID);
            $appName = Config::get('app_name');
            $invoiceLogo = Config::get('invoiceLogo');
            $fileNameToStore = 'Gain-' . $order->invoice_id . '.pdf';

            $pdf = PDF::loadView('invoice.invoiceTemplate',
                compact('templateData', 'orderItems', 'order', 'appName', 'invoiceLogo')
            );

            $content = $pdf->download()->getOriginalContent();
            Storage::put('public/pdf/'.$fileNameToStore,$content);

            $emailSend = new Email;
            $emailSend->sendEmail($mailText, $email, $subject, $fileNameToStore);

            unlink(public_path('/storage/pdf/' . $fileNameToStore));

        } catch (\Exception $e) {
            return $e;
        }
    }

    public function formatOrdersDetails($orderID, $cashRegister)
    {

        $orderDetails = Order::getInvoiceData($orderID, $cashRegister);

        $allSettingFormat = new AllSettingFormat();
        $orderDetails->due = $orderDetails->total - $orderDetails->paid;

        $orderDetails->paid = $allSettingFormat->getCurrencySeparator($orderDetails->paid);
        $orderDetails->total = $allSettingFormat->getCurrencySeparator($orderDetails->total);
        $orderDetails->sub_total = $allSettingFormat->getCurrencySeparator($orderDetails->sub_total);
        $orderDetails->change = $allSettingFormat->getCurrencySeparator($orderDetails->change);
        $orderDetails->date = $allSettingFormat->getDate($orderDetails->date);


        if ($orderDetails->change == null) {
            $orderDetails->change = 0;
        }

        return $orderDetails;
    }

    public static function formatOrdersItems($orderID)
    {
        $orderItems = OrderItems::getOrderDetails($orderID, true);
        $allSettingFormat = new AllSettingFormat();
        foreach ($orderItems as $item) {
            if ($item->type == 'discount') {
                $item->price = null;
                $item->quantity = null;
                $item->discount = null;
                $item->total = $allSettingFormat->getCurrencySeparator($item->sub_total);
            } else {
                $item->discount = $item->discount . '%';
                $item->price = $allSettingFormat->getCurrencySeparator($item->price);
                $item->total = $allSettingFormat->getCurrencySeparator($item->sub_total);
            }
        }

        return $orderItems;
    }

    public function setSalesReceivingType(Request $request)
    {
        $salesOrReceivingType = $request->salesOrReceivingType;
        $orderType = $request->orderType;
        Setting::saveSalesOrReceivingType($salesOrReceivingType, $orderType);
    }

    public function setPurchaseGrnType(Request $request)
    {
        $salesOrReturnType = $request->salesOrReturnType;
       
        Setting::savePurchaseGrnType($salesOrReturnType);
    }

    public function getPurchaseGrnType()
    {
        return Setting::getPurchaseGrnType();
    }
    
    

    public function saleListDelete($id)
    {
        $delete = Order::salesListDelete($id);
        if ($delete == 0) {
            $response = [
                'message' => Lang::get('lang.sales_list_small') . ' ' . Lang::get('lang.successfully_deleted')
            ];

            return response()->json($response, 200);
        }
    }

    
    public static function saleListRemarksUpdate(Request $request, $id)
    {

        $remarks    = trim(addslashes($request->remarks));
        $update     = Order::where('id', '=', $id)
            ->update(['sales_note' => $remarks]);         
    
            $response = [
                'message' => Lang::get('lang.remarks') . ' ' . Lang::get('lang.successfully_updated')
            ];

            return response()->json($response, 200);
         
    }

    public static function saleListUpdate(Request $request, $id)
    {

        $date = Carbon::parse($request->editedSalesDate)->format('Y-m-d');
        $shippingStatus = $request->shippingStatus;
        $update = Order::where('id', '=', $id)
            ->update(['date' => $date]);
        $updateShipping = ShippingInformation::where('order_id', '=', $id)
            ->update(['status' => $shippingStatus]);

        if ($update == 1 && $updateShipping == 1) {
            $response = [
                'message' => Lang::get('lang.sales_date') . ' ' . Lang::get('lang.successfully_updated')
            ];

            return response()->json($response, 200);
        }
    }

    public function offlineSalesStore(Request $request)
    {
        $numberOfOrdersPlaced = count($request->all());
        $count = 0;
        //DB::beginTransaction();
        foreach ($request->all() as $singleOrder) {

            $dueAmount = 0;
            $customerId = 0;

            if ($singleOrder['isCashRegisterBranch'] == true) {
                $cashRegisterID = $singleOrder['cashRagisterId'];
            } else $cashRegisterID = null;

            //profit
            if ($singleOrder['profit'] == null) $profit = 0;
            else $profit = $singleOrder['profit'];

            //discount
            $allDiscount = 0;

            if (array_key_exists('discount', $singleOrder) && $singleOrder['discount'] != null) {
                $allDiscount = $singleOrder['discount'];
            }

            //due
            if ($singleOrder['status'] == 'done') {
                if (!empty($singleOrder['payments'])) {
                    foreach ($singleOrder['payments'] as $key => $value) {
                        if ($value['paymentType'] == 'credit') {
                            $dueAmount = floatval($value['paid']);
                        }
                    }
                }
            }
            $orderData = array();

            //customer id / supplier id
            if ($singleOrder['orderType'] == 'sales') {
                if (array_key_exists('customer', $singleOrder) && $singleOrder['customer'] != null) {
                    if (array_key_exists('id', $singleOrder['customer'])) {
                        $orderData['customer_id'] = $singleOrder['customer']['id'];
                        $customerId = $orderData['customer_id'];
                    } else {
                        $orderData['customer_id'] = Customer::getInsertedId($singleOrder['customer']);
                        $customerId = $orderData['customer_id'];
                    }
                } else {
                    $orderData['customer_id'] = null;
                }
            } else {
                if (array_key_exists('customer', $singleOrder) && $singleOrder['customer'] != null) {
                    if (array_key_exists('id', $singleOrder['customer'])) {
                        $orderData['supplier_id'] = $singleOrder['customer']['id'];
                    } else {
                        $orderData['supplier_id'] = Supplier::getInsertedId($singleOrder['customer']);
                    }
                } else {
                    $orderData['supplier_id'] = null;
                }
            }
            if (array_key_exists('transferBranch', $singleOrder)) $orderData['transfer_branch_id'] = $singleOrder['transferBranch'];

            $orderData['date'] = Carbon::parse($singleOrder['date']);
            $orderData['order_type'] = $singleOrder['orderType'];
            $orderData['sales_note'] = $singleOrder['salesNote'];
            $orderData['sub_total'] = $singleOrder['subTotal'];
            $orderData['total_tax'] = $singleOrder['tax'];
            $orderData['due_amount'] = $dueAmount;
            $orderData['total'] = $singleOrder['grandTotal'];
            $orderData['type'] = $singleOrder['salesOrReceivingType'];
            $orderData['profit'] = $profit;
            $orderData['status'] = $singleOrder['status'];
            $orderData['all_discount'] = $allDiscount;
            $orderData['table_id'] = $singleOrder['tableId'];


            if (array_key_exists('selectedBranchID', $singleOrder)) $orderData['branch_id'] = $singleOrder['selectedBranchID'];
            if (array_key_exists('invoice_id', $singleOrder)) $orderData['invoice_id'] = $singleOrder['invoice_id'];
            if (isset($singleOrder['invoice_id'])) $invoiceIdExistOrNot = Order::getIdOfExisted('invoice_id', $singleOrder['invoice_id']);

            $orderData['created_by'] = $singleOrder['createdBy'];
            $orderData['created_at'] = Carbon::parse($singleOrder['time']);


            if ($singleOrder['orderID'] != null) {
                Order::updateData($singleOrder['orderID'], $orderData);
                $orderID = $singleOrder['orderID'];
            } else {
                $orderLastId = Order::store($orderData);
                $orderID = $orderLastId->id;
            }

            $lastUpdatedInvoice = Setting::where('setting_name', 'last_invoice_number')->first()->setting_value;

            if (empty($invoiceIdExistOrNot) && array_key_exists('current_invoice_number', $singleOrder)) {
                if ($singleOrder['current_invoice_number'] > $lastUpdatedInvoice) {
                    Setting::updateSetting('last_invoice_number', $singleOrder['current_invoice_number']);
                }
            }

            $orderItems = [];
            //cart data insert in order_items
            foreach ($singleOrder['cart'] as $cart) {

                if ($singleOrder['orderType'] == 'sales') $quantity = -$cart['quantity'];
                else $quantity = $cart['quantity'];

                if ($singleOrder['orderType'] == 'receiving'
                    || $singleOrder['salesOrReceivingType'] == 'internal'
                    || $singleOrder['salesOrReceivingType'] == 'internal-transfer') {
                    $this->updateVariantPurchasePrice($cart);
                } else {
                    $this->updateVariantSalesPrice($cart);
                }
                if (!array_key_exists('discount', $cart) || $cart['discount'] == null) $cart['discount'] = 0;

                array_push($orderItems, [
                    'product_id' => $cart['productID'],
                    'variant_id' => $cart['variantID'],
                    'type' => $cart['orderType'],
                    'quantity' => $quantity,
                    'price' => $cart['price'],
                    'discount' => $cart['discount'],
                    'sub_total' => $cart["calculatedPrice"],
                    'tax_id' => $cart['taxID'],
                    'order_id' => $orderID,
                    'note' => $cart['cartItemNote'],
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            // payment items insert in payment table
            if ($singleOrder['status'] == 'done') {
                if (sizeof($singleOrder['payments']) > 0) {
                    $paymentArray = [];

                    foreach ($singleOrder['payments'] as $rowPayment) {
                        array_push($paymentArray, [
                            'date' => Carbon::parse($singleOrder['date']),
                            'paid' => $rowPayment['paid'],
                            'exchange' => $rowPayment['exchange'],
                            'payment_method' => $rowPayment['paymentID'],
                            'options' => serialize($rowPayment['options']),
                            'order_id' => $orderID,
                            'cash_register_id' => $cashRegisterID,
                            'is_active' => $rowPayment['is_active'],
                            'created_at' => $rowPayment['PaymentTime']
                        ]);
                    }

                    Payments::insertData($paymentArray);
                }
            }
            if ($singleOrder['status'] == 'done' || $singleOrder['status'] == 'cancelled') {
                if ($singleOrder['orderID'] != null) OrderItems::deleteRecord('order_id', $singleOrder['orderID']);
                OrderItems::insertData($orderItems);

                //Send email and generate invoice
                try {
                    $salesOrReceivingType = $singleOrder['salesOrReceivingType'];
                    $transferBranchName = $request->transferBranchName;

                    $invoiceTemplateEmail = new InvoiceTemplateController();
                    $invoiceTemplateData = $invoiceTemplateEmail->getInvoiceTemplateToPrint($orderID, $salesOrReceivingType, $transferBranchName, $cashRegisterID, $singleOrder['orderType'], 'email');

                    $autoEmailReceive = Setting::getSettingValue('auto_email_receive')->setting_value;

                    if ($customerId) {
                        $orderCustomer = Customer::getOne($customerId);

                        if ($autoEmailReceive == 1 && $orderCustomer->email) {

                            $content = EmailTemplate::select('template_subject', 'default_content', 'custom_content')->where('template_type', 'pos_invoice')->first();
                            $subject = $content->template_subject;

                            if ($content->custom_content) $text = $content->custom_content;
                            else $text = $content->default_content;

                            $mailText = str_replace('{first_name}', $orderCustomer->first_name, str_replace('{invoice_id}', $singleOrder['invoice_id'], str_replace('{app_name}', Config::get('app_name'), $text)));


                            $this->sendPdf($invoiceTemplateData['data'], $orderID, $cashRegisterID, $mailText, $orderCustomer->email, $subject);
                        }
                    }
                } catch (\Exception $e) {
                }
                $count++;
            } elseif ($singleOrder['status'] == 'hold') {
                if ($singleOrder['orderID'] != null) OrderItems::deleteRecord('order_id', $singleOrder['orderID']);
                OrderItems::insertData($orderItems);
                $count++;
            } else {
                $count--;
            }
        }

        $lastInvoiceNumber = Setting::where('setting_name', 'last_invoice_number')->first()->setting_value;

        if ($numberOfOrdersPlaced == $count) {
            //DB::commit();
            $response = [
                'message' => Lang::get('lang.sync_complete_your_all_sales_now_up_to_date'),
                'lastInvoiceNumber' => $lastInvoiceNumber
            ];
            return response()->json($response, 201);
        } else {
            //DB::rollback();
            $response = [
                'message' => Lang::get('lang.something_went_wrong'),
            ];
            return response()->json($response, 400);
        }
    }

    public function salesListGetData(Request $request, $id)
    {
        set_time_limit(3000);
        if ($request->columnKey) $columnName = $request->columnKey;
        if ($request->rowLimit) $limit = $request->rowLimit;
        $filtersData = $request->filtersData;
        $searchValue = searchHelper::inputSearch($request->searchValue);
        $requestType = $request->reqType;
        $due = OrderItems::salesListItems($id, $filtersData, $searchValue, $request->columnSortedBy, $limit, $request->rowOffset, $columnName, $requestType);
        // $salesListItemsSubTotals = OrderItems::salesListItemsSubTotals($id, $filtersData, $searchValue, $request->columnSortedBy, $limit, $request->rowOffset, $columnName, $requestType);

 

        if (empty($requestType)) $dueData = $due['data'];
        else $dueData = $due;

        if (empty($requestType)) {
            $totalCount = 1000;
 
            // $dueData[] = [
            //     'invoice_id' => Lang::get('lang.total'),
            //     'item_purchased' =>$salesListItemsSubTotals['total_item_purchased'],
            //     'tax' => $salesListItemsSubTotals['total_tax'],
            //     'discount' => $salesListItemsSubTotals['total_discount'],
            //     'total' => $salesListItemsSubTotals['total_order_total'],
            //     'due_amount' => $salesListItemsSubTotals['total_due_amount'],
            // ];
            // $dueData[] = [
            //         'invoice_id' => Lang::get('lang.grand_total'),
            //         'item_purchased' => 10,
            //         'tax' => 10,
            //         'discount' => 10,
            //         'total' => 100,
            //         'due_amount' => 200
            //     ];

            // $dueData[$arrayCount] = [
            //     'invoice_id' => Lang::get('lang.total'),
            //     'item_purchased' => $dueItems['netItem'],
            //     'tax' => $dueItems['netTax'],
            //     'discount' => $dueItems['discount'],
            //     'total' => $dueItems['netTotal'],
            //     'due_amount' => $dueItems['netDueAmount'],
            // ];
            // $grandCalculation = $this->calculateDues($due['allData']);
            // $dueData[$arrayCount + 1] = [
            //     'invoice_id' => Lang::get('lang.grand_total'),
            //     'item_purchased' => number_format($grandCalculation['netItem'],2),
            //     'tax' => $grandCalculation['netTax'],
            //     'discount' => $grandCalculation['discount'],
            //     'total' => $grandCalculation['netTotal'],
            //     'due_amount' => $grandCalculation['netDueAmount']
            // ];

            return ['datarows' => $dueData, 'count' => $totalCount];

        } else {

            $this->calculateDues($dueData);
            return ['datarows' => $dueData];
        }
    }

    public function calculateDues($dueData)
    {
        $netTotal = 0;
        $netTax = 0;
        $netItem = 0;
        $arrayCount = 0;
        $netDiscount = 0;
        $netDueAmount = 0;

        foreach ($dueData as $rowData) {
            if ($rowData->type == 'customer') {
                $rowData->type = Lang::get('lang.customer');
            } else {
                $rowData->type = Lang::get('lang.internal_sales');
                $rowData->customer = $rowData->transfer_branch_name;
            }
            if ($rowData->due_amount > 0) {
                $rowData->payment_status = Lang::get('lang.due');;
            } else {
                $rowData->payment_status = Lang::get('lang.paid');
            }
            if ($rowData->customer == '') $rowData->customer = Lang::get('lang.walk_in_customer');
            $netTax += $rowData->tax;
            $netTotal += $rowData->total;
            $netItem += $rowData->item_purchased;
            $netDiscount += $rowData->discount;
            $netDueAmount += $rowData->due_amount;
            $arrayCount++;
        }

        return [
            'netTotal' => $netTotal,
            'netTax' => $netTax,
            'netItem' => $netItem,
            'discount' => $netDiscount,
            'count' => $arrayCount,
            'netDueAmount' => $netDueAmount
        ];
    }

    public function storeShippingInformation($request, $orderID)
    {
        $ShippingData = array();
        $ShippingData['shipping_area_id'] = $request->shippingAreaId;
        $ShippingData['price'] = $request->shippingPrice;
        $ShippingData['shipping_address'] = $request->shippingAreaSddress;
        $ShippingData['order_id'] = $orderID;
        $ShippingData['branch_id'] = $request->branchId;

        ShippingInformation::store($ShippingData);
    }

    public function insertInternalTransfer($orderData, $transferBranch, $userBranchId, $invoiceFixes, $purchaseLastInvoiceNumber)
    {
        $orderData['order_type'] = 'receiving';
        $orderData['branch_id'] = $transferBranch;
        $orderData['transfer_branch_id'] = $userBranchId;
        $orderLastIdForInternalTransfer = Order::store($orderData);
        $orderIdInternalTransfer = $orderLastIdForInternalTransfer->id;

        Order::updateData($orderIdInternalTransfer, ['invoice_id' => $invoiceFixes['purchasePrefix'] . $purchaseLastInvoiceNumber . $invoiceFixes['purchaseSuffix']]);
        $purchaseLastInvoiceNumber += 1;

        $purchaseLastUpdatedInvoice = Setting::where('setting_name', 'purchase_last_invoice_number')->first()->setting_value;
        if ($purchaseLastInvoiceNumber > $purchaseLastUpdatedInvoice) {
            Setting::updateSetting('purchase_last_invoice_number', $purchaseLastInvoiceNumber);
        }

        return $orderIdInternalTransfer;
    }

    public function sendPOSupplier(Request $request){
        $invoiceID      = $request->invoiceID;        
        $orderDetails   = Order::orderDetailsByInvoiceId($invoiceID);
        $orderCustomer  = Supplier::getOne($orderDetails->supplier_id);   
        
        $orderID        = $orderDetails->id;
        $salesOrReceivingType = $orderDetails->sales_or_receiving_type;;
        $transferBranchName = null;
        $cashRegister   = $orderDetails->cash_register_id;
        $orderType      = $orderDetails->order_type;
        $email      = $orderCustomer->email ? trim($orderCustomer->email) : '';

        if($email == '' || empty($email) ){
            $response = [
                'status' => false,
                'message' => "Supplier doesn't have email "
            ];
            return response()->json($response, 210);
        }

        
        $invoiceTemplateEmail = new InvoiceTemplateController();
        $invoiceTemplateData = $invoiceTemplateEmail->getInvoiceTemplateToPrint($orderID, $salesOrReceivingType, $transferBranchName, $cashRegister, $orderType, 'email');



        $subject    = 'Purchase Order of '.$invoiceID;
            
            $salesReportEmailsAry[] = $email;

            $mailText   = "<html> 
                            <body> 
                            <p>Hello, </p>
                            
                            <p>Hope you're having a great day. <br>
                            Purchase Invoice is now available. <br>
                            Please find the below attachment.</p>
                           
                            <p>Thanks, <br> <br>
                            MBU POS</p>

                            </body>
                            </html>";

        
            $emailSend = new Email;     
            $fileNameToStore = "Sales Report ".date('dMY').".pdf";
            $pdf = PDF::loadView('reports.po', [ 'purchaseInvoice' => $invoiceTemplateData['data']  ] )
            ->setPaper('a4', 'landscape')
            ->setWarnings(false);

            $content    = $pdf->download()->getOriginalContent();

            Storage::put('public/pdf/'.$fileNameToStore,$content);
            $url = env('APP_URL');
            $_pdf =     $url.'public/pdf/'.$fileNameToStore;
            $_pdf =     storage_path('app/public/pdf/'.$fileNameToStore);   

            if(!empty($salesReportEmailsAry)){
                foreach ($salesReportEmailsAry as $key => $email) {
                    $emailSend->sendEmail($mailText, $email, $subject, $fileNameToStore);
                }
                
            }
            
            unlink($_pdf); 

            $response = [
                'status' => true,
                'message' => "Sent Purchase Invoice to Supplier Successfully."
            ];
            return response()->json($response, 200);

    }

    public function generateGRNOrder(Request $request){
        
        // Validate the incoming request
        $validator = Validator::make($request->all(), [
            'orderId' => 'required|integer',
            'cart' => 'required|array',
        ], [
            'orderId.required' => 'The order ID is required.',
            'orderId.integer' => 'The order ID must be an integer.',
            'cart.required' => 'The cart is required.',
            'cart.array' => 'The cart must be an array.',
        ]);
    
        // Check if validation fails
        if ($validator->fails()) {
            // Return a response with validation errors
            return response()->json(['errors' => $validator->errors()], 422);
        }
    
        // Validation passed, proceed with the request
        $validated          = $validator->validated();
        $orderId            = $validated['orderId'];
        $_carts             = $validated['cart'];
        $orderNote          = $request->orderNote;        
        $grnInvocieDateIn   = $request->grnInvocieDateIn;
        $grnInvocieNumberIn = $request->grnInvocieNumberIn;
        $formattedDate      = Carbon::parse($grnInvocieDateIn)->format('Y-m-d');

        

        $carts = [];
        if(!empty($_carts)){
            foreach($_carts as $key => $rec){
                $_rec = [];
                $_rec = $rec;
                // $_rec['quantity'] = $rec['receivingQuantity'] + $rec['quantity'];
                $_rec['received_quantity'] = (isset($rec['receivingQuantity']) ? $rec['receivingQuantity'] : 0) + $rec['quantity'];
                $_rec['quantity'] = (isset($rec['receivingQuantity']) ? $rec['receivingQuantity'] : 0);
                $_rec['grn_status'] = 'receiving';
                if($_rec['received_quantity'] == $_rec['ordered_quantity']){
                    $_rec['grn_status'] = 'done';
                }
                $carts[] = $_rec;
            }
        }
    
        // Get the maximum GRN invoice ID for the order
        $_invoiceId = Order::where('id', $orderId)->pluck('invoice_id')->first();  
        $maxGrnInvoiceId = Order::where('invoice_id', $_invoiceId)->max('grn_invoice_id');  

        $existingOrder = Order::findOrFail($orderId); 
        $newOrder = $existingOrder->replicate();
        $newOrder->save();
        $replicatedId = $newOrder->id;

        $invoiceData = $this->invoiceData();
        $grnprefix = $invoiceData['grnInvoicePrefix'];
        $grnsuffix = $invoiceData['grnInvoiceSuffix'];
        $grnlastInvoice = intval($invoiceData['grnLastInvoiceNum']) + 1;
        $grnInvoiceID = $grnprefix . strval($grnlastInvoice) . $grnsuffix;

        \DB::table('orders')
        ->where('id', $replicatedId)
        ->update(['sales_note' => $orderNote,
         'date' => date('Y-m-d'), 'grn_invoice_id' =>  (int)$maxGrnInvoiceId+1, 'grn_invoice' => $grnInvoiceID]);

         if($grnInvocieNumberIn != ""){
            \DB::table('orders')
            ->where('id', $replicatedId)
            ->update([
            'grn_invoice_number_ref' => $grnInvocieNumberIn,
            'grn_invoice_ref_date' => $formattedDate]);
         }
        
         Setting::updateSetting('grn_last_invoice_number', $grnlastInvoice);
    
        // Iterate through the cart items and create order items
        foreach ($carts as $cart) {
            $inAry = [
                'product_id' => $cart['productID'],
                'variant_id' => $cart['variantID'],
                'type' => $cart['orderType'],
                'ordered_quantity' => $cart['ordered_quantity'],
                'quantity' => $cart['quantity'], // Assuming this is the received quantity
                'price' => $cart['price'],
                'discount' => $cart['discount'],
                'sub_total' => $cart['calculatedPrice'],
                'tax_id' => $cart['taxID'],
                'order_id' => $replicatedId, // Use the ID of the newly created order
                'note' => $cart['cartItemNote'],
                'created_at' => now(), // Use Laravel's now() helper to get the current timestamp
                'updated_at' => now(),
                'grn_status' => $cart['grn_status'],
            ];
          
            OrderItems::create($inAry);
        }
        

        $OrderInfo = Order::where('id', $orderId)->first();        
        $latestInvoiceId =  isset($OrderInfo->invoice_id)  ? $OrderInfo->invoice_id : null;

        $OrderInfo = Order::where('id', $replicatedId)->first();        
        $latestGrnInvoiceId =  isset($OrderInfo->grn_invoice_id)  ? $OrderInfo->grn_invoice_id : null;

        

        if($latestInvoiceId){
            $orderItems = Order::join('order_items', function ($join) use ($latestInvoiceId, $latestGrnInvoiceId) {
                $join->on('order_items.order_id', '=', 'orders.id')
                    ->where('orders.invoice_id', '=', $latestInvoiceId)
                    ->where('orders.grn_invoice_id', '=', $latestGrnInvoiceId)
                    ->where('order_items.grn_status', '!=', 'done');
            })
            ->count();
  

            if($orderItems == 0){ 
                \DB::table('orders')
                ->where('invoice_id', $latestInvoiceId)
                ->update(['grn_order_status' => 'done']);
            }
        }

        #invoice template code 

        $cashRegister = $request->cashRagisterId;
      
        $userId = Auth::id();
        $cashRegister = CashRegisterLog::where('user_id', $userId)
            ->where('status', 'open')
            ->orderBy('id', 'desc')
            ->first();
 

            $_POST['is_grn']  = $request->is_grn;
      
        $invoiceTemplate = new InvoiceTemplateController();

        $paymentArray =  ['date' => Carbon::now(),  'payment_method' => "1", 'cash_register_id' => "1", 'order_id' => $OrderInfo->id,  'created_at' => Carbon::now()];
        Payments::insertData($paymentArray);

        // $templateData = $invoiceTemplate->getInvoiceTemplateToPrint($replicatedId, "receiving", null, $cashRegister->cash_register_id, 'receiving', 'receipt');
        $templateData = $invoiceTemplate->getInvoiceTemplateToPrint($replicatedId, "receiving", null, $cashRegister->cash_register_id, 'grn', 'receipt');
        #end 

        $productDetails = $this->getOrderItemssummary1($OrderInfo->id);
        $productData = "";
        $slNO = 1;
        foreach ($productDetails['data'] as $item) {
            $productData .= "<tr>";
            $productData .= "<tr><td>".$slNO++."</td>";
            $productData .= "<td>".$item->title."</td>";
            $productData .= "<td>".$item->short_name."</td>";
            $productData .= "<td>".$item->orderquantity."</td>";
            $productData .= "<td>".$item->receivedquantity."</td>";
            $productData .= "</tr>";
        }

        $productData = "";
        foreach ($productDetails['data'] as $item) {
            $productData .= "<tr>";
            $productData .= "<td colspan='4' style='text-align: center;'>".$item->title."</td>";
            $productData .= "</tr>";
            
            $itemsList = $this->getOrderItemssummaryDetails($orderId);
            foreach($itemsList as $rec){
                $productData .= "<tr>";
                $productData .= "<td >".$rec->course."</td>";
                $productData .= "<td >".$rec->item."</td>";
                $productData .= "<td class='text-center'>".$rec->item_size."</td>";
                $productData .= "<td class='text-center'>".$item->receivedquantity."</td>";
                $productData .= "</tr>";
            }
            
        }
        
        $templateData = str_replace('{product_details}', $productData, $templateData);
        $templateData = str_replace('{remarks}', $orderNote, $templateData);
        $templateData = str_replace('{po_date}', date("d/m/Y"), $templateData);
        $templateData = str_replace('{grninvoice_id}', $grnInvoiceID, $templateData);
        $templateData = str_replace('{current_date}', date("d/m/Y"), $templateData);


        $orderDetails = Order::where(['id' => $OrderInfo->id])->first();

        if($orderDetails->grn_invoice_number_ref){
            $invoiceInfo = ' <div class="info-row">
        <div class="info-label">Invoice Number</div>
        <div class="info-value">:&nbsp; &nbsp; '.$orderDetails->grn_invoice_number_ref.' &  '.date('d/m/Y', strtotime($orderDetails->grn_invoice_ref_date)).'</div>
        </div>';
            $templateData = str_replace('{invoice_info}', $invoiceInfo, $templateData); 
        }else{
            $templateData = str_replace('{invoice_info}','', $templateData); 
        }    


        
        $data['order'] = $newOrder;
        $data['invoiceTemplate'] = $templateData;
    
        // Return a JSON response with the new order
        return response()->json($data, 200);
    }
    
    public function storeQuotations(Request $request)
    {
        $records = $request->all(); 
        $createdBy = Auth::user()->id;
        $quotations = [];
    
        foreach ($records['records'] as $index => $record) {
            // Validate each record
            $validator = Validator::make([
                'supplier_id' => $record['supplier_id'],
                'total' => $record['total'],
                'order_id' => $record['order_id'],
                'file_upload' => $request->file("records.$index.file_upload")
            ], [
                'supplier_id' => 'required',
                'total' => 'required',
                'order_id' => 'required',
                'file_upload' => 'required|file'
            ]);
    
            if ($validator->fails()) {
                return response()->json(['error' => $validator->errors()], 400);
            }
    
            // Handle file upload
            if ($request->hasFile("records.$index.file_upload") && $request->file("records.$index.file_upload")->isValid()) {
                $filePath = $request->file("records.$index.file_upload")->store('public/uploads');
                $fileName = basename($filePath);
            } else {
                return response()->json(['error' => 'File upload failed'], 400);
            }
    
            // Create purchase quotation
            $quotation = PurchaseQuotation::create([
                'order_id' => $record['order_id'],
                'supplier_id' => $record['supplier_id'],
                'file_upload' => $fileName,
                'total' => $record['total'],
                'created_by' => $createdBy
            ]);
            $quotations[] =  $quotation->id; 
        }
        return response()->json(['success' => 'Quotations saved successfully', 'data' => $quotations], 201);
    }


    public function getApprovalsList()
    {
        $url = env('APP_URL');
        $response = PurchaseQuotation::query()
        ->join('orders', 'orders.id', '=', 'purchase_quotations.order_id')
        ->join('order_items', 'order_items.order_id', '=', 'orders.id')
        ->leftJoin('users', 'users.id', '=', 'orders.created_by')
        ->leftJoin('suppliers', 'suppliers.id', '=', 'orders.supplier_id')
        ->select(
            'purchase_quotations.status',
            'orders.id as order_id',
            'orders.date',
            'orders.supplier_id',
            DB::raw('CAST(ROUND(orders.total, 2) AS DECIMAL(20, 2)) as total'), 
            'orders.invoice_id',
            'suppliers.id as supplier_id',
            DB::raw('CONVERT(ABS(SUM(order_items.ordered_quantity)), SIGNED INTEGER) as item_purchased'),
            DB::raw("CONCAT(users.first_name,' ',users.last_name) AS full_name"),
            DB::raw("CONCAT(suppliers.first_name,' ',suppliers.last_name) AS supplier_name"),
            DB::raw("CONCAT('$url', '/api/receiving/', orders.id) AS url") 
            )
        ->groupBy(
            'orders.id',
            'orders.supplier_id',
            'orders.date',
            'orders.total',
            'orders.invoice_id',
            'suppliers.id',
            'users.first_name',
            'users.last_name',
            'suppliers.first_name',
            'suppliers.last_name',
        )
        // ->where('orders.status', '!=', 'cancelled')
        ->orderBy('purchase_quotations.id', 'DESC')
        ->get();
    
        return $response;
    }


    public function getSupplierQuotes($id){
        $suppliers = PurchaseQuotation::Join('suppliers', 'suppliers.id', '=', 'purchase_quotations.supplier_id')
                        ->select('purchase_quotations.*',  DB::raw("CONCAT(suppliers.first_name,' ',suppliers.last_name) AS supplier_name"),
                          DB::raw('CAST(ROUND(purchase_quotations.total, 2) AS DECIMAL(20, 2)) as total') )
                        ->where('order_id', $id)->get();

        return response()->json(['success' => 'suppliers details fetched successfully', 'data' => $suppliers], 201);
    }


    public function updateSupplier(Request $request) {
        $order_id = $request->orderId;
        $supplier_id = $request->supplier_id;
        $status = $request->status;
    
        $response = Order::updateData($order_id, ['supplier_id' => $supplier_id]);
        PurchaseQuotation::where('order_id', '=', $order_id)->update(['status' => $status]);
        return response()->json(['success' => 'supplier details updated successfully', 'data' => $response], 201);
    }

    public function checkOrderId($id)
    {
        $exists = PurchaseQuotation::where('order_id', $id)->exists();
        return response()->json(['data' => $exists], 200);
    }
    
    public function purchaseQuotationCancel(Request $request)
    {
        $orderId = $request->orderId;
        if (Order::checkExists('id', $orderId)) {
            Order::updateData($orderId, ['status' => 'cancelled']);
            if(PurchaseQuotation::checkExists('order_id', $orderId)){
                PurchaseQuotation::where('order_id', $orderId)->update(['status' => 'Cancelled']);
            }
        }
    }
}
