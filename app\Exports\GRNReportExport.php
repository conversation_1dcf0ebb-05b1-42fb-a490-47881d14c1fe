<?php

namespace App\Exports;

use App\Http\Controllers\API\PermissionController;
use App\Models\OrderItems;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;

class GRNReportExport implements WithHeadings, FromQuery, WithMapping, WithEvents
{
    use Exportable;
    public $filterData;
    public $searchValue;

    function __construct($filterData, $searchValue){
        $this->filterData = $filterData; 
        $this->searchValue = $searchValue;
    }

    public function query()
    {
        $_searchValue = $this->searchValue;
        $_filterData  = $this->filterData;

        $perm = new PermissionController();
        $permission = $perm->checkReceivingPermission();

        $query = OrderItems::query()->join('orders', 'orders.id', '=', 'order_items.order_id')
            ->join('users', 'users.id', '=', 'orders.created_by')
            ->leftJoin('suppliers', 'suppliers.id', '=', 'orders.supplier_id')
            ->select(
                'orders.id',
                'orders.date',
                'orders.type',
                'orders.status',
                'orders.total',
                'orders.invoice_id',
                'orders.sales_note',
                'orders.grn_invoice',
                'orders.due_amount',
                'orders.grn_order_status',
                'orders.grn_invoice_number_ref',
                DB::raw("DATE_FORMAT(orders.grn_invoice_ref_date, '%d/%m/%Y') AS grn_invoice_ref_date"), 
                DB::raw("suppliers.id as supplier_id"),
                DB::raw('CONVERT(abs(sum(order_items.quantity)),SIGNED INTEGER) as item_purchased'),
                DB::raw("CONCAT(users.first_name,' ',users.last_name)  AS full_name"),
                DB::raw("CONCAT(suppliers.first_name,' ',suppliers.last_name)  AS supplier_name"),
                DB::raw("users.id as user_id")
            )
            ->where('orders.grn_invoice_id', '!=', "0")
            ->where('orders.order_type', '=', 'receiving')
            ->groupBy('order_items.order_id');

        if ($permission == 'personal') {
            $query->where('orders.created_by', Auth::user()->id);
        }

        if (!empty($_filterData)) {

            foreach ($_filterData as $singleFilter) {

                if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "receive_type") {
                    if ($singleFilter['value'] == 'returns') {
                        $query->where('orders.total', '<', 0);
                    } else if ($singleFilter['value'] == 'internal-transfer') {
                        $query->where('orders.type', $singleFilter['value']);
                    } else $query->where('orders.type', $singleFilter['value']);

                } else if (array_key_exists('filterKey', $singleFilter) && $singleFilter['filterKey'] == "date_range") {

                    $query->whereBetween('orders.date', [$singleFilter['value'][0]['start'], $singleFilter['value'][0]['end']]);

                } else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "grn_order_status" && $singleFilter['value'] != "all") {

                    if ($singleFilter['value'] == 'done') {
                        $query->where('orders.grn_order_status', '=', 'done');
                    } elseif ($singleFilter['value'] == 'receiving') {
                        $query->where('orders.grn_order_status', '=', 'receiving');
                    }
                }
                else if (array_key_exists('key', $singleFilter) && $singleFilter['key'] == "supplier" && $singleFilter['value'] != "all") {
                    $query->where('suppliers.id', '=', $singleFilter['value']);
                }
            }
        }
        if (!empty($_searchValue)) {
            $query->where(function ($query) use ($_searchValue) {
                $query->where('orders.grn_invoice', 'LIKE', '%' . $_searchValue . '%')
                    ->orWhere('orders.invoice_id', 'LIKE', '%' . $_searchValue . '%');
            });
        }
       return $query;
    }

    public function map($reportRow): array
    {
        return [
            $reportRow->invoice_id,
            $reportRow->grn_invoice,
            $reportRow->date,
            $reportRow->supplier_name ??  Lang::get('lang.walk_in_supplier'),
            $reportRow->grn_invoice_number_ref,
            $reportRow->grn_invoice_ref_date,
            $reportRow->full_name,
            $reportRow->grn_order_status,
            $reportRow->item_purchased,
        ];
    }

    public function headings(): array
    {
        return [
            Lang::get('lang.invoice_id'),
            Lang::get('lang.grn_invoice_id'),
            Lang::get('lang.date'),
            Lang::get('lang.supplier_name'),
            "Supplier Invocie ID",
            "Supplier Invocie Date",
            Lang::get('lang.received_by'),
            Lang::get('lang.status'),
            Lang::get('lang.items_received'),
        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $event->sheet->getStyle('A1:G1')->applyFromArray([
                        'font' =>[
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' =>[
                                'color' => ['argb' =>'FFFF0000']
                            ],
                        ]
                    ],
                );
                
                $query_result = $this->query()->get();
                $rows_count = count($query_result);

                $items_received = $query_result->sum('item_purchased');

                $rows_count = $rows_count + 2;

                $event->sheet->setCellValue('A'.$rows_count, 'Total');
                $event->sheet->setCellValue('I'.$rows_count, number_format($items_received));

            },
        ];
    }
}
