<?php

namespace App\Models;
use DB;
use Illuminate\Database\Eloquent\Model;

class CommonModel extends Model
{
    public static function insert_data($param) {
        return DB::table($param['table'])->insert($param['data']);
    }

    public static function insert_data_id($param) {
        return DB::table($param['table'])->insertGetId($param['data']);
    }

    public static function update_data($param) {
        $query = DB::table($param['table']);

        if(isset($param['where'])) {
            $query->where($param['where']);
        }
        if(isset($param['where_in'])) {
            $query->whereIn($param['where_in']['column'], $param['where_in']['data']);
        }
		return  $query->update($param['data']);
    }

    public static function get_data($param)
    {
        $query = DB::table($param['table']);
        if(isset($param['fields'])){
            $query->select($param['fields']);
        }
        if(isset($param['fields_raw'])) {
            $query->addSelect(DB::raw($param['fields_raw']));
        }
        if(isset($param['left_join'])) {
            foreach($param['left_join'] as $join) {
                $query->leftJoin($join['table'], $join['left_side'], '=', $join['right_side']);
            }
        }
        if(isset($param['where'])) {
            $query->where($param['where']);
        }
        if(isset($param['where_raw'])) {
            $query->whereRaw($param['where_raw']);
        }
        if(isset($param['where_not_equal'])) {
            foreach ($param['where_not_equal'] as $key => $value) {
                $query->where($key,'!=',$value);
            }
        }
        if(isset($param['where_bt']['column'])){
            $query->whereBetween($param['where_bt']['column'], [$param['where_bt']['from_date'], $param['where_bt']['to_date']]);
        }
        if(isset($param['search'])) {
            $searchColumnsArr = $param['search']['search_columns_arr'];
            $searchString = $param['search']['search_string'];
            $query->where(function($query) use ($searchColumnsArr,$searchString) { 
                foreach ($searchColumnsArr as $index => $column) {
                    if($index == 0)
                        $query->where($column, 'LIKE', '%'.$searchString.'%');
                    else
                        $query->orWhere($column, 'LIKE', '%'.$searchString.'%');
                }           
            });     
        }
        if(isset($param['groupby'])) {
            $query->groupBy($param['groupby']);
        }
        if(isset($param['order_by'])) {
            $query->orderBy($param['order_by']['column'], $param['order_by']['direction']);
        }
        if(isset($param['m_order_by'])) {
            foreach ($param['m_order_by'] as $key => $value) {
                $query->orderBy($key, $value);
            }
        }
        $count = $query->get()->count();
        if(isset($param['limit'])) {
            $query->offset($param['limit']['offset']);
            $query->limit($param['limit']['limit']);
        }
        $data = $query->get();
        return ['datarows' => $data, 'count' => $count];
    }

    public static function get_row($param) {
        $query = DB::table($param['table']);

        if(isset($param['fields'])) {
            $query->select($param['fields']);
        }
        if(isset($param['fields_raw'])) {
            $query->select(DB::raw($param['fields_raw']));
        }
        if(isset($param['left_join'])) {
            foreach($param['left_join'] as $join) {
                $query->leftJoin($join['table'], $join['left_side'], '=', $join['right_side']);
            }
        }
        if(isset($param['where'])) {
            $query->where($param['where']);
        }
        if(isset($param['where_raw'])) {
            $query->whereRaw($param['where_raw']);
        }
        if(isset($param['orwhere'])) {
            $query->orWhere($param['orwhere']);
        }
        $return = $query->first();
        return $return;
    }

    public static function delete_data($param) {
        $query = DB::table($param['table']);
        
        if(isset($param['where'])) {
            $query->where($param['where']);
        }

        if(isset($param['where_in'])) {
            foreach ($param['where_in'] as $key => $array) {
                $query->whereIn($key, $array);
            }
        }
        return $query->delete();
    }
}