<?php

namespace App\Exports;

use App\Models\OrderItems;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;

class SalesDetailsReportExport implements WithHeadings, FromQuery, WithMapping, WithEvents
{
    use Exportable;

    public function query()
    {
        return  OrderItems::query()->leftjoin('products', 'order_items.product_id', '=', 'products.id')
        ->leftJoin('orders', 'order_items.order_id', '=', 'orders.id')
        ->leftJoin('product_variants', 'order_items.variant_id', '=', 'product_variants.id')
        ->leftJoin('product_units', 'products.unit_id', '=', 'product_units.id')
        ->select(
            'products.brand_id',
            'products.category_id',
            'products.group_id',
            'order_items.sub_total',
            'orders.date',
            'orders.invoice_id',
            'order_items.type',
            'product_units.name',
            DB::raw('(CASE WHEN order_items.product_id = 0
            THEN (CASE WHEN order_items.type = "shipment" THEN "Shipment" ELSE "Discount" END) ELSE concat(title,if(variant_title="default_variant"," ",concat("(",product_variants.variant_title,")"))) END) as title'),
            DB::raw('(CASE WHEN order_items.type = "discount" THEN 0 ELSE (CASE WHEN ROUND(order_items.quantity,2) > 0 THEN ((-1)*ROUND(order_items.quantity,2)) ELSE abs(ROUND(order_items.quantity,2)) END) END) as quantity'),
            'order_items.discount'
        )
        ->where('orders.order_type', '=', 'sales')
        ->where('orders.status', '=', 'done')
        ->orderBy('orders.invoice_id', 'desc');
    }

    public function map($reportRow): array
    {       
        return 
            [
                $reportRow->invoice_id,
                date('d/m/Y', strtotime($reportRow->date)),
                $reportRow->title,
                number_format((float)$reportRow->quantity, 2, '.', ''),
                $reportRow->name,
                number_format((float)$reportRow->sub_total, 2, '.', ''),
            ];                    
    }

    public function headings(): array
    {
        return [
           "Invoice ID", "Date", "Items", "Quantity", "Unit", "Sub Total"
        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                $event->sheet->getStyle('A1:H1')->applyFromArray([
                        'font' =>[
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' =>[
                                'color' => ['argb' =>'FFFF0000']
                            ],
                        ]
                    ],
                );
                
                
                $query_result = $this->query()->get();
                $rows_count = count($query_result);
                    
                $quantity  = $query_result->sum('quantity');  
                $total          = $query_result->sum('sub_total');
                $rows_count = $rows_count + 3;
                                
                $event->sheet->setCellValue('A'.$rows_count, 'Grand Total'); 
                $event->sheet->setCellValue('C'.$rows_count, number_format($quantity));
                $event->sheet->setCellValue('E'.$rows_count, number_format($total,2));
                
            },
        ];
    }
    
}
