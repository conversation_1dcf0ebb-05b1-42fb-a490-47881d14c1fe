<?php

namespace App\Exports;

use App\Models\Customer;
use App\Models\CustomerGroup;
use App\Models\CustomerPayment;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;

class CustomersExport implements WithHeadings, FromQuery, WithMapping, WithEvents
{
    use Exportable;

    public function query()
    {
        return Customer::query();
    }

    public function map($customer): array
    {
        if ($customer->customer_group) {
            $customer_group = CustomerGroup::where('id', $customer->customer_group)->pluck('title')->first();
        } else {
            $customer_group = '';
        }

        $uniform_payment = CustomerPayment::where('customer_id', $customer->id)->first();

        return [
            $customer->id ?? null,
            $customer->first_name ?? null,
            $customer->last_name ?? null,
            $customer->roll_no ?? null,
            $customer->company ?? null,
            $customer->email ?? null,
            $customer->phone_number ?? null,
            $customer_group ?? null,
            $uniform_payment->fees ?? null,
            $uniform_payment->paid ?? null,
            $uniform_payment->balance ?? null
        ];

    }

    public function headings(): array
    {
        return [
            'ID',
            'First Name',
            'Last Name',
            'Roll No',
            'Course',
            'Email',
            'Phone Number',
            'Customer Group',
            'Uniform Total Fee',
            'Uniform Paid Fee',
            'Uniform Balance Fee'
        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:K1')->applyFromArray(
                    [
                        'font' => [
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' => [
                                'color' => ['argb' => 'FFFF0000']
                            ],
                        ]
                    ],
                );
            },
        ];
    }
}
