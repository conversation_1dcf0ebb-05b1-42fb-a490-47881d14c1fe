.todo-list .hover-box {
  cursor: pointer;
  opacity: 0; }
  .todo-list .hover-box i {
    padding: 5px 0;
    display: inline-block; }
  .todo-list .hover-box:hover {
    color: #4a97fd; }
.todo-list .media {
  padding: 16px;
  color: #999999;
  background-color: #ffffff;
  margin: 0;
  box-shadow: none; }
  .todo-list .media:hover .hover-box {
    opacity: 1;
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -ms-transition: all 0.5s;
    -o-transition: all 0.5s;
    transition: all 0.5s; }
.todo-list .media-border {
  border-bottom: 1px solid #eeeeee; }
  .todo-list .media-border:first-child {
    border-top: 1px solid #eeeeee; }
.todo-list .media-xs-padding {
  padding: 8px; }
.todo-list .media-sm-padding {
  padding: 10px; }
.todo-list .hover:hover {
  color: #999999;
  background-color: #eee; }
.todo-list .read {
  color: #999999;
  background-color: #ffffff; }
.todo-list .unread {
  background-color: #eee;
  color: #999999; }

/*# sourceMappingURL=todo-list.css.map */
