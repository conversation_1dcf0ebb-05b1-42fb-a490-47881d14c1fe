<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerPayment extends Model
{
    use HasFactory;

    protected $table = 'customer_payments';

    protected $fillable = [
        'customer_id', 'fees', 'balance', 'paid'
    ];

    // Optionally, if you want to specify custom primary key or other options
    protected $primaryKey = 'id';
    public $timestamps = true;
}
