.layout-table {
  display: table; }

.layout-table-cell {
  display: table-cell; }

.inner-left-panel {
  width: 350px;
  vertical-align: top; }

.inner-right-panel {
  padding: 0 0 0 30px;
  vertical-align: top; }

@media (max-width: 800px) {
  .inner-right-panel .filter-left-items .btn-sm {
    padding: 0.5rem 1.8rem; }
  .inner-right-panel .filter-right-items .page-search {
    width: 100%; } }
@media (max-width: 575px) {
  .layout-table {
    display: block !important; }

  .inner-left-panel {
    display: block !important;
    width: 100%; }

  .inner-right-panel {
    display: block;
    padding: 0; }
    .inner-right-panel .page-header {
      margin: 30px 0; }
    .inner-right-panel .page-header .filter-left-items {
      width: 100%;
      margin-bottom: 10px; }
      .inner-right-panel .page-header .filter-left-items .filter-item {
        margin-right: 1.25rem; }
    .inner-right-panel .page-header .filter-right-items .filter-item:last-child {
      margin-bottom: 0 !important; } }
@media (min-width: 320px) and (max-width: 568px) and (orientation: portrait) {
  .inner-right-panel .filter-right-items .page-search {
    width: 94%; } }

/*# sourceMappingURL=responsive-layout.css.map */
