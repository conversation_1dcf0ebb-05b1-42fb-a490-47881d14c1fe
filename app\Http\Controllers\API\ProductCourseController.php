<?php

namespace App\Http\Controllers\API;

use App\Models\Product;
use App\Models\ProductCourse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Validator;

class ProductCourseController extends Controller
{

    public function getCourses(Request $request)
    {
        $productUnit = ProductCourse::getCourseData($request);
      

        return ['datarows' => $productUnit['data'], 'count' => $productUnit['count']];
    }
    public function getCoursesList(Request $request)
    {
        $productUnit = ProductCourse::getCourseList($request);
      

        return $productUnit;
    }

    public function getProductsList(Request $request){
        return Product::leftJoin('product_categories', 'product_categories.id', '=', 'products.category_id')
        ->leftJoin('product_brands', 'product_brands.id', '=', 'products.brand_id')
        ->leftJoin('product_groups', 'product_groups.id', '=', 'products.group_id')
        ->select('products.id',  'product_categories.name as cat_name', 'product_brands.name as brand_name', 'product_groups.name as group_name',
        DB::raw("CONCAT(product_categories.name, ' - ', products.title) as title"))
        ->orderBy('products.id', 'ASC')
        ->get();
    }
    

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|unique:product_courses,course_name', 
        ],
        [
            'name.required' => 'The course name is required.',
            'name.unique' => 'The course name is already taken.', 
        ]);
        
        if ($validator->fails()) {
            $response = [
                'message' => $validator->errors()->first()
            ];

            return response()->json($response, 404);
        }

        if ($productUnit = ProductCourse::store([
            'course_name' => $request->input('name'), 
            'created_by' => Auth::user()->id
        ])) {
            $response = [
                'message' => Lang::get('lang.course') . ' ' . Lang::get('lang.successfully_saved')
            ];

            return response()->json($response, 201);
        } else {
            $response = [
                'message' => Lang::get('lang.getting_problems')
            ];

            return response()->json($response, 404);
        }
    }

    public function show($id)
    {
       return ProductCourse::getOne($id);
    }

    public function update(Request $request, $id)
    {

        $validator = Validator::make($request->all(), [
            'name' => 'required|unique:product_courses,course_name,'.$id, 
        ],
        [
            'name.required' => 'The course name is required.',
            'name.unique' => 'The course name is already taken.', 
        ]);
        
        if ($validator->fails()) {
            $response = [
                'message' => $validator->errors()->first()
            ];

            return response()->json($response, 404);
        }

        $productUnit = ProductCourse::getOne($id);

        if ($productUnit) {
            $unitData = array(
                'course_name' => $request->input('name') 
            );

            ProductCourse::updateData($id, $unitData);

            $response = [
                'message' => Lang::get('lang.course') . ' ' . Lang::get('lang.successfully_updated')
            ];

            return response()->json($response, 201);
        } else {
            $response = [
                'message' => Lang::get('lang.getting_problems')
            ];

            return response()->json($response, 404);
        }
    }

    public function delete($id)
    {
        // $used = Product::countRecord('unit_id', $id);

        // if ($used == 0) {
            ProductCourse::deleteData($id);
            $response = [
                'message' => Lang::get('lang.course') . ' ' . Lang::get('lang.successfully_deleted')
            ];

            return response()->json($response, 201);
        // } else {
        //     $response = [
        //         'message' => Lang::get('lang.course').' '.Lang::get('lang.in_use').', '.Lang::get('lang.you_can_not_delete_the').' '.strtolower(Lang::get('lang.unit'))

        //     ];

        //     return response()->json($response, 200);
        // }
    }
   
}
