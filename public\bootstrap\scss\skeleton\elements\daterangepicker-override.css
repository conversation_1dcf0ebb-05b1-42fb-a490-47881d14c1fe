.daterangepicker {
  padding: 0;
  border: none;
  border-radius: 2px;
  background-color: #ffffff;
  box-shadow: 0 2px 10px 1px rgba(0, 0, 0, 0.1); }
  .daterangepicker::before, .daterangepicker::after {
    display: none; }
  .daterangepicker .ranges {
    margin: 0; }
    .daterangepicker .ranges ul {
      width: 100%; }
      .daterangepicker .ranges ul li {
        border: none;
        border-radius: 0;
        padding: 0.5rem 1.5rem;
        margin: 0;
        background-color: #ffffff;
        color: #999999;
        font-size: 1rem; }
        .daterangepicker .ranges ul li:hover {
          background-color: #4a97fd;
          color: #ffffff; }
      .daterangepicker .ranges ul li.active {
        background-color: #4a97fd;
        color: #ffffff; }
    .daterangepicker .ranges .range_inputs {
      margin: 2px 0 5px 0; }
  .daterangepicker .daterangepicker_input .input-mini {
    display: inline-block;
    border-radius: 2px;
    padding: 2px 10px;
    border: 1px solid #eeeeee;
    color: #999999;
    height: inherit; }
  .daterangepicker .daterangepicker_input .active {
    border-color: #4a97fd; }
  .daterangepicker .calendar-table th.prev i::before, .daterangepicker .calendar-table th.next i::before {
    font-family: "fontello";
    font-style: normal; }
  .daterangepicker .calendar-table th.prev:hover, .daterangepicker .calendar-table th.next:hover {
    background-color: transparent;
    color: #4a97fd; }
  .daterangepicker .calendar-table th.prev i::before {
    content: '\E85E'; }
  .daterangepicker .calendar-table th.next i::before {
    content: '\E85F'; }
  .daterangepicker .calendar-table td.start-date {
    border-radius: 2px 0 0 2px; }
  .daterangepicker .calendar-table td.end-date {
    border-radius: 0 2px 2px 0; }
  .daterangepicker .calendar-table td {
    color: #999999;
    border-radius: 2px; }
  .daterangepicker .calendar-table td.in-range {
    background-color: #4996FD12; }
  .daterangepicker .calendar-table td.active {
    background-color: #4a97fd;
    color: #ffffff; }
    .daterangepicker .calendar-table td.active:hover {
      background-color: #4a97fd; }

/*# sourceMappingURL=daterangepicker-override.css.map */
