<?php

namespace App\Exports;

use App\Http\Controllers\API\PermissionController;
use App\Libraries\AllSettingFormat;
use App\Models\Payments;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Events\AfterSheet;
use Illuminate\Support\Facades\Auth;

class PaymentsReportExport implements WithHeadings, FromQuery, WithMapping, WithEvents
{
    use Exportable;

    public function query()
    {
        $perm = new PermissionController();
        $permission = $perm->checkPaymentPermission();

        $payments = Payments::select(
            'payments.id',
            'payments.order_id',
            'payments.date',
            // 'payments.paid', 
            'orders.total as paid',
            'orders.invoice_id as invoice_id',
            'payments.exchange',
            'payment_types.name as payment_method',
            'orders.customer_id',
            'orders.order_type',
            'orders.created_by',
            'cash_registers.title as cash_register',
            DB::raw('(CASE WHEN orders.order_type = "sales" THEN concat(customers.first_name," ", customers.last_name) ELSE concat(users.first_name," ", users.last_name) END) as paid_by'),
            DB::raw('(CASE WHEN orders.order_type = "sales" THEN orders.customer_id ELSE orders.created_by END) as paid_id'),
            'orders.sales_note as refno'
        )
            ->leftJoin('payment_types', 'payment_types.id', '=', 'payments.payment_method')
            ->leftJoin('cash_registers', 'cash_registers.id', '=', 'payments.cash_register_id')
            ->leftJoin('orders', 'payments.order_id', '=', 'orders.id')
            ->leftJoin('users', 'users.id', '=', 'orders.created_by')
            ->leftJoin('customers', 'customers.id', '=', 'orders.customer_id')
            ->where('orders.status', '=', 'done');

        if ($permission == 'personal') {
            $payments->where('orders.created_by', Auth::user()->id);
        }
        return  $payments->orderBy('payments.id', 'DESC');
    }

    public function map($reportRow): array
    {
        $allSettingFormat = new AllSettingFormat;
        return
            [
                $reportRow->invoice_id,
                $reportRow->date,
                $reportRow->payment_method,
                $reportRow->paid_by,
                $reportRow->refno,
                $reportRow->cash_register,
                $allSettingFormat->getCurrencySeparator(number_format((float) $reportRow->paid, 2, '.', '')),
            ];
    }

    public function headings(): array
    {
        return [
            "Invoice ID",
            "Date",
            "Method",
            "Paid By",
            "Ref. No.",
            "Cash Register",
            "Amount",
        ];
    }

    //Highlight heading column
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getStyle('A1:G1')->applyFromArray(
                    [
                        'font' => [
                            'bold' => true
                        ],
                        'borders' => [
                            'outline' => [
                                'color' => ['argb' => 'FFFF0000']
                            ],
                        ]
                    ],
                );

                $allSettingFormat = new AllSettingFormat;

                $query_result = $this->query()->get();
                $rows_count = count($query_result);

                $total = $query_result->sum('paid');
                $rows_count = $rows_count + 2;

                $event->sheet->setCellValue('A' . $rows_count, 'Grand Total');
                $event->sheet->setCellValue('G' . $rows_count, $allSettingFormat->getCurrencySeparator($total));

            },
        ];
    }

}
